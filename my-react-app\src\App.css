/* Add Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  /* Enhanced color palette */
  --primary-color: #0e3b7d; /* Deep blue - professional, sports-related */
  --primary-dark: #092c61; /* Darker shade for hover states */
  --primary-light: #2a5cb3; /* Lighter shade for accents */
  --secondary-color: #e63946; /* Accent red - energy, excitement */
  --secondary-dark: #c62836; /* Darker shade for hover states */
  --secondary-light: #f05c68; /* Lighter shade for accents */
  --dark-color: #1d3557; /* Dark blue for text and backgrounds */
  --light-color: #f1faee; /* Off-white for backgrounds */
  --light-color-alt: #e9f5e1; /* Alternative light background */
  --gray-color: #457b9d; /* Muted blue for secondary elements */
  --gray-light: #a8dadc; /* Lighter blue for subtle elements */
  --text-color: #1d3557; /* Dark blue for text */
  --text-light: #f1faee; /* Light text */
  --accent-color: #ffd166; /* Yellow accent for highlights */
  --success-color: #06d6a0; /* Green for success messages */

  /* Enhanced shadows */
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 10px 25px rgba(0, 0, 0, 0.15);
  --box-shadow-strong: 0 10px 30px rgba(0, 0, 0, 0.2);
  --box-shadow-inset: inset 0 2px 5px rgba(0, 0, 0, 0.05);
  --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  /* Enhanced transitions */
  --transition-fast: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-slow: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Border radius */
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 20px;
  --border-radius-sm: 4px;
  --border-radius-round: 50%;

  /* Z-index layers */
  --z-background: -1;
  --z-normal: 1;
  --z-overlay: 10;
  --z-dropdown: 20;
  --z-fixed: 30;
  --z-modal: 40;
  --z-tooltip: 50;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Poppins', 'Montserrat', 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--light-color);
  overflow-x: hidden;
  scroll-behavior: smooth;
  transition: background-color 0.5s ease;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(168, 218, 220, 0.1), transparent 60%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.05), transparent 60%);
  pointer-events: none;
  z-index: var(--z-background);
}

/* Add smooth scrolling to all elements */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* Accounts for fixed header */
}

/* Enhanced typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--dark-color);
  letter-spacing: -0.02em;
  position: relative;
}

h1 {
  font-size: 3.5rem;
  letter-spacing: -0.03em;
}

h2 {
  font-size: 2.8rem;
}

h3 {
  font-size: 2.2rem;
}

h4 {
  font-size: 1.8rem;
}

h5 {
  font-size: 1.4rem;
}

h6 {
  font-size: 1.2rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.05rem;
  line-height: 1.7;
  color: var(--text-color);
}

#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Page loader styles */
.section-loader,
.page-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.page-loader {
  height: 70vh;
  background-color: transparent;
  box-shadow: none;
}

.loader-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(14, 59, 125, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--secondary-color);
}

/* Google Fonts already imported at the top */

/* Utility classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem 1.8rem;
  background-color: var(--primary-color);
  color: var(--text-light);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  transition: var(--transition-bounce);
  text-align: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 4px 12px rgba(14, 59, 125, 0.2);
  font-size: 0.95rem;
  gap: 0.5rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--secondary-color);
  transition: width 0.3s ease;
  z-index: -1;
}

.btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.3);
}

.btn:hover::before {
  width: 100%;
}

.btn:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(14, 59, 125, 0.2);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(14, 59, 125, 0.1);
}

.btn-outline::before {
  background-color: var(--primary-color);
}

.btn-outline:hover {
  color: var(--text-light);
}

.btn-secondary {
  background-color: var(--secondary-color);
  box-shadow: 0 4px 12px rgba(230, 57, 70, 0.2);
}

.btn-secondary::before {
  background-color: var(--secondary-dark);
}

.btn-secondary:hover {
  box-shadow: 0 8px 20px rgba(230, 57, 70, 0.3);
}

.btn-lg {
  padding: 1rem 2.2rem;
  font-size: 1.1rem;
}

.btn-sm {
  padding: 0.6rem 1.2rem;
  font-size: 0.85rem;
}

.card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
  backface-visibility: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(14, 59, 125, 0.03) 0%, rgba(230, 57, 70, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
  pointer-events: none;
}

.card:hover {
  transform: translateY(-8px) scale(1.01);
  box-shadow: var(--box-shadow-hover);
}

.card:hover::before {
  opacity: 1;
}

/* Enhanced Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInSlow {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.85);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes textClip {
  0% {
    background-position: -100%;
  }
  100% {
    background-position: 200%;
  }
}

@keyframes textShadowPulse {
  0% {
    text-shadow: 0 0 5px rgba(14, 59, 125, 0.3);
  }
  50% {
    text-shadow: 0 0 10px rgba(14, 59, 125, 0.5);
  }
  100% {
    text-shadow: 0 0 5px rgba(14, 59, 125, 0.3);
  }
}

@keyframes borderGlow {
  0% {
    box-shadow: 0 0 5px rgba(230, 57, 70, 0.3);
  }
  50% {
    box-shadow: 0 0 10px rgba(230, 57, 70, 0.5);
  }
  100% {
    box-shadow: 0 0 5px rgba(230, 57, 70, 0.3);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes flipIn {
  0% {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateY(-10deg);
  }
  70% {
    transform: perspective(400px) rotateY(10deg);
  }
  100% {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes glowPulse {
  0% {
    filter: drop-shadow(0 0 5px rgba(14, 59, 125, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(14, 59, 125, 0.8)) drop-shadow(0 0 20px rgba(230, 57, 70, 0.4));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(14, 59, 125, 0.5));
  }
}

/* Section loader styles */
.section-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 3rem;
  text-align: center;
}

.loader-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(14, 59, 125, 0.2);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.section-loader p {
  color: var(--gray-color);
  font-size: 1.1rem;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animation utility classes */
.fade-in {
  animation: fadeIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.fade-in-slow {
  animation: fadeInSlow 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.slide-in-left {
  animation: slideInLeft 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.slide-in-right {
  animation: slideInRight 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.scale-in {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.float {
  animation: float 3s ease-in-out infinite;
}

.shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.rotate {
  animation: rotate 10s linear infinite;
}

.gradient-bg {
  background: linear-gradient(270deg, var(--primary-color), var(--secondary-color), var(--primary-light));
  background-size: 200% 200%;
  animation: gradientBG 8s ease infinite;
}

.text-gradient {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--primary-color));
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textClip 4s linear infinite;
}

.text-shadow-pulse {
  animation: textShadowPulse 2s ease-in-out infinite;
}

.border-glow {
  animation: borderGlow 2s ease-in-out infinite;
}

.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.ripple:focus:not(:active)::after {
  animation: ripple 0.6s ease-out;
}

.bounce {
  animation: bounce 2s ease infinite;
}

.shake {
  animation: shake 0.5s ease-in-out;
}

.flip-in {
  animation: flipIn 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  backface-visibility: visible !important;
}

.glow-pulse {
  animation: glowPulse 2s ease-in-out infinite;
}

/* Animation delays */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary-color), var(--gray-color));
  border-radius: 10px;
  border: 3px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--primary-dark), var(--primary-color));
}
