/* ===== CSS VARIABLES ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-dark: #111827;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), #ea580c);
    --font-primary: 'Inter', sans-serif;
    --font-display: 'Playfair Display', serif;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --container-max-width: 1200px;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
}

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
    color: white;
}

.loader-circle {
    width: 60px;
    height: 60px;
    border: 3px solid rgba(255,255,255,0.1);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loader-text {
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.8;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255,255,255,0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.nav-profile-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    font-weight: 500;
    color: var(--text-secondary);
    transition: color var(--transition-fast);
    position: relative;
    padding: 0.5rem 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
    border-radius: 2px;
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 2s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4));
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 2rem;
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.title-line {
    display: block;
}

.title-line.accent {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    opacity: 0.7;
}

.scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.scroll-arrow {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== SERVICES OVERVIEW ===== */
.services-overview {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0.05;
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 0;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all var(--transition-normal);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
    transition: all var(--transition-fast);
}

.service-link:hover {
    gap: 1rem;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: 6rem 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text .section-title {
    text-align: left;
    margin-bottom: 2rem;
}

.about-text .section-title::after {
    left: 0;
    transform: none;
}

.about-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.about-highlights {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.highlight {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 500;
}

.highlight i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.about-image {
    position: relative;
}

.profile-image {
    width: 100%;
    max-width: 400px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    margin-left: auto;
}

.image-decoration {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: var(--gradient-accent);
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;
}

/* ===== PORTFOLIO SECTIONS ===== */
.videography-section,
.photography-section,
.graphics-section,
.events-section {
    padding: 6rem 0;
}

.videography-section {
    background: var(--bg-secondary);
}

.events-section {
    background: var(--bg-secondary);
}

/* Filter Buttons */
.video-filters,
.photo-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    background: white;
    color: var(--text-secondary);
    font-weight: 500;
    border: 2px solid var(--border-color);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Grid Layouts */
.video-grid,
.photo-grid,
.graphics-grid,
.events-grid {
    display: grid;
    gap: 2rem;
}

.video-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.photo-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.graphics-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.events-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Card Styles */
.video-card,
.photo-card,
.graphics-card,
.event-card {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.video-card:hover,
.photo-card:hover,
.graphics-card:hover,
.event-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.card-image {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.video-card:hover .card-image img,
.photo-card:hover .card-image img,
.graphics-card:hover .card-image img,
.event-card:hover .card-image img {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.video-card:hover .card-overlay,
.photo-card:hover .card-overlay,
.graphics-card:hover .card-overlay,
.event-card:hover .card-overlay {
    opacity: 1;
}

.play-button {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: all var(--transition-normal);
}

.play-button:hover {
    transform: scale(1.1);
    background: var(--secondary-color);
}

.card-content {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.card-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-light);
}

.card-category {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 6rem 0;
    background: var(--bg-dark);
    color: white;
}

.contact-section .section-title,
.contact-section .section-subtitle {
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-card {
    background: rgba(255,255,255,0.05);
    padding: 2rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all var(--transition-normal);
}

.contact-card:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-5px);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.contact-card h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.contact-card p {
    color: rgba(255,255,255,0.8);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all var(--transition-normal);
}

.social-link:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}

/* Contact Form */
.contact-form {
    background: rgba(255,255,255,0.05);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.2);
    border-radius: var(--border-radius);
    color: white;
    font-size: 1rem;
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255,255,255,0.15);
}

.form-group label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: rgba(255,255,255,0.7);
    transition: all var(--transition-normal);
    pointer-events: none;
}

.form-group input:focus + label,
.form-group input:valid + label,
.form-group select:focus + label,
.form-group select:valid + label,
.form-group textarea:focus + label,
.form-group textarea:valid + label {
    top: -0.5rem;
    left: 0.5rem;
    font-size: 0.8rem;
    color: var(--primary-color);
    background: var(--bg-dark);
    padding: 0 0.5rem;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255,255,255,0.5);
}

/* ===== FOOTER ===== */
.footer {
    background: #000;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    text-align: center;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.footer-logo span {
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-text {
    color: rgba(255,255,255,0.7);
    max-width: 500px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: rgba(255,255,255,0.7);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 1rem;
    text-align: center;
    color: rgba(255,255,255,0.5);
    font-size: 0.9rem;
}

/* ===== LIGHTBOX ===== */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    text-align: center;
}

.lightbox-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: var(--border-radius);
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.lightbox-close:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

.lightbox-caption {
    color: white;
    margin-top: 1rem;
    font-size: 1.1rem;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    width: 100%;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    background: rgba(255,255,255,0.1);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    pointer-events: all;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

.lightbox-prev {
    left: -80px;
}

.lightbox-next {
    right: -80px;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

.fade-in-left {
    animation: fadeInLeft 0.6s ease forwards;
}

.fade-in-right {
    animation: fadeInRight 0.6s ease forwards;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .video-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .graphics-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255,255,255,0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left var(--transition-normal);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-title {
        font-size: clamp(2.5rem, 6vw, 4rem);
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .video-filters,
    .photo-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .video-grid,
    .photo-grid,
    .graphics-grid,
    .events-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .card-image {
        height: 200px;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }

    .lightbox-prev {
        left: 10px;
    }

    .lightbox-next {
        right: 10px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: clamp(2rem, 4vw, 3rem);
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .card-content {
        padding: 1rem;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .contact-card {
        padding: 1.5rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden {
    display: none;
}

.visible {
    display: block;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
