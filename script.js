// ===== GLOBAL VARIABLES =====
let currentImageIndex = 0;
let currentImages = [];
let heroSlideIndex = 0;

// ===== PORTFOLIO DATA =====
const portfolioData = {
    videos: [
        {
            id: 1,
            title: "Championship Game Highlights",
            description: "Professional coverage of the championship game with multiple camera angles and expert commentary.",
            category: "sports",
            thumbnail: "images/sports/750_0350-Enhanced-NR.jpg",
            videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
        },
        {
            id: 2,
            title: "Live Sports Broadcasting",
            description: "Real-time sports broadcasting with professional commentary and analysis.",
            category: "broadcast",
            thumbnail: "images/sports/DSC00156-Enhanced-NR.jpg",
            videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
        },
        {
            id: 3,
            title: "Player Profile Documentary",
            description: "In-depth documentary showcasing athlete's journey and achievements.",
            category: "highlights",
            thumbnail: "images/sports/DSC05711-Enhanced-NR.jpg",
            videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
        },
        {
            id: 4,
            title: "Game Analysis Breakdown",
            description: "Detailed analysis of game strategies and key moments.",
            category: "broadcast",
            thumbnail: "images/sports/DSC08306-Enhanced-NR.jpg",
            videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
        },
        {
            id: 5,
            title: "Season Highlights Reel",
            description: "Compilation of the best moments from the entire season.",
            category: "highlights",
            thumbnail: "images/sports/DSC09229-Enhanced-NR.jpg",
            videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
        },
        {
            id: 6,
            title: "Behind the Scenes",
            description: "Exclusive behind-the-scenes footage of team preparation.",
            category: "sports",
            thumbnail: "images/sports/DSC_5152-Enhanced-NR.jpg",
            videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
        }
    ],
    
    photos: [
        { id: 1, title: "Victory Celebration", category: "action", src: "images/sports/750_0350-Enhanced-NR.jpg" },
        { id: 2, title: "Intense Focus", category: "portraits", src: "images/sports/750_0441-Enhanced-NR.jpg" },
        { id: 3, title: "Team Strategy", category: "events", src: "images/sports/DSC00061-Enhanced-NR.jpg" },
        { id: 4, title: "Athletic Excellence", category: "action", src: "images/sports/DSC00113-Enhanced-NR.jpg" },
        { id: 5, title: "Championship Moment", category: "events", src: "images/sports/DSC00156-Enhanced-NR.jpg" },
        { id: 6, title: "Game Intensity", category: "action", src: "images/sports/DSC00231-Enhanced-NR.jpg" },
        { id: 7, title: "Precision Shot", category: "action", src: "images/sports/DSC00732-Enhanced-NR.jpg" },
        { id: 8, title: "Athletic Form", category: "portraits", src: "images/sports/DSC04917-Enhanced-NR.jpg" },
        { id: 9, title: "Competitive Spirit", category: "portraits", src: "images/sports/DSC05591-Enhanced-NR.jpg" },
        { id: 10, title: "Victory Moment", category: "events", src: "images/sports/DSC05711-Enhanced-NR.jpg" },
        { id: 11, title: "Team Unity", category: "events", src: "images/sports/DSC06401-Enhanced-NR.jpg" },
        { id: 12, title: "Athletic Power", category: "action", src: "images/sports/DSC06411-Enhanced-NR.jpg" },
        { id: 13, title: "Game Dynamics", category: "action", src: "images/sports/DSC07025.jpg" },
        { id: 14, title: "Focused Determination", category: "portraits", src: "images/sports/DSC07308-Enhanced-NR.jpg" },
        { id: 15, title: "Strategic Play", category: "action", src: "images/sports/DSC07394-Enhanced-NR.jpg" },
        { id: 16, title: "Excellence Display", category: "action", src: "images/sports/DSC08306-Enhanced-NR.jpg" },
        { id: 17, title: "Competitive Edge", category: "portraits", src: "images/sports/DSC08558-Enhanced-NR.jpg" },
        { id: 18, title: "Championship Drive", category: "events", src: "images/sports/DSC08928.jpg" },
        { id: 19, title: "Team Coordination", category: "action", src: "images/sports/DSC09229-Enhanced-NR.jpg" },
        { id: 20, title: "Athletic Precision", category: "action", src: "images/sports/DSC09584-Enhanced-NR.jpg" },
        { id: 21, title: "Victory Achievement", category: "events", src: "images/sports/DSC09840-Enhanced-NR.jpg" },
        { id: 22, title: "Game Atmosphere", category: "events", src: "images/sports/DSC_1913.jpg" },
        { id: 23, title: "Athletic Mastery", category: "portraits", src: "images/sports/DSC_4909-Enhanced-NR.jpg" },
        { id: 24, title: "Championship Glory", category: "events", src: "images/sports/DSC_5152-Enhanced-NR.jpg" },
        { id: 25, title: "Competitive Fire", category: "portraits", src: "images/sports/DSC_5338-Enhanced-NR.jpg" },
        { id: 26, title: "Team Spirit", category: "events", src: "images/sports/DSC_5376.jpg" },
        { id: 27, title: "Athletic Achievement", category: "action", src: "images/sports/DSC_5608-Enhanced-NR.jpg" },
        { id: 28, title: "Championship Legacy", category: "events", src: "images/sports/DSC_5713.jpg" }
    ],
    
    graphics: [
        { id: 1, title: "Team Branding Package", category: "branding", src: "images/graphics/graphic1.png" },
        { id: 2, title: "Player Highlight Design", category: "sports", src: "images/graphics/graphic2.png" },
        { id: 3, title: "Championship Poster", category: "marketing", src: "images/graphics/graphic3.png" },
        { id: 4, title: "Social Media Graphics", category: "digital", src: "images/graphics/graphic4.png" },
        { id: 5, title: "Event Promotion Design", category: "marketing", src: "images/graphics/graphic5.png" },
        { id: 6, title: "Sports Logo Design", category: "branding", src: "images/graphics/graphic6.png" },
        { id: 7, title: "Game Day Graphics", category: "sports", src: "images/graphics/graphic7.png" },
        { id: 8, title: "Digital Banner Set", category: "digital", src: "images/graphics/graphic8.png" },
        { id: 9, title: "Team Merchandise Design", category: "branding", src: "images/graphics/graphic9.png" },
        { id: 10, title: "Player Feature Graphic", category: "sports", src: "images/graphics/graphic10.png" },
        { id: 11, title: "Aziyah Player Feature", category: "sports", src: "images/graphics/aziyah f copy.png" },
        { id: 12, title: "Championship Deck", category: "presentation", src: "images/graphics/deck final.png" },
        { id: 13, title: "Indianapolis Schedule", category: "marketing", src: "images/graphics/indy schedule w_ home away.png" },
        { id: 14, title: "Indianapolis Branding", category: "branding", src: "images/graphics/indy-v1.png" },
        { id: 15, title: "Liberty Championship Social", category: "digital", src: "images/graphics/liberty title social.png" },
        { id: 16, title: "On-Air Graphics Package", category: "broadcast", src: "images/graphics/on air graphic.png" },
        { id: 17, title: "PS5 Game Cover Design", category: "marketing", src: "images/graphics/ps5 cover.png" },
        { id: 18, title: "Sherman Shadowcats Branding", category: "branding", src: "images/graphics/sherman shadowcats graphic copy.png" },
        { id: 19, title: "Snider Player Feature", category: "sports", src: "images/graphics/snider landyn.png" },
        { id: 20, title: "Trent Perry Feature", category: "sports", src: "images/graphics/trent perry copy.png" }
    ],
    
    events: [
        { id: 1, title: "Championship Finals Coverage", description: "Complete coverage of the championship finals with multiple angles and professional documentation.", src: "images/events/event1.jpg" },
        { id: 2, title: "Sports Awards Ceremony", description: "Professional documentation of the annual sports awards ceremony and recognition event.", src: "images/events/event2.jpg" },
        { id: 3, title: "Team Building Workshop", description: "Corporate team building event with comprehensive photo and video coverage.", src: "images/events/event3.jpg" },
        { id: 4, title: "Youth Sports Tournament", description: "Multi-day youth sports tournament with complete event documentation and highlights.", src: "images/events/event4.jpg" },
        { id: 5, title: "Sports Conference Summit", description: "Professional conference coverage including keynote speeches and networking sessions.", src: "images/events/event5.jpg" },
        { id: 6, title: "Athletic Hall of Fame Induction", description: "Prestigious hall of fame induction ceremony with complete event coverage and interviews.", src: "images/events/event6.jpg" }
    ]
};

// ===== DOM CONTENT LOADED =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });

    // Initialize all components
    initNavigation();
    initPageNavigation();
    initHeroSlider();
    initPortfolioSections();
    initLightbox();
    initContactForm();
    initEnhancedContactForm();
    initScrollEffects();
    initFAQ();

    // Hide loading screen
    setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }, 1500);
});

// ===== NAVIGATION =====
function initNavigation() {
    const navbar = document.getElementById('navbar');
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        });
    });
    
    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Active link highlighting
    const sections = document.querySelectorAll('section[id]');
    
    window.addEventListener('scroll', () => {
        const scrollY = window.pageYOffset;
        
        sections.forEach(section => {
            const sectionHeight = section.offsetHeight;
            const sectionTop = section.offsetTop - 100;
            const sectionId = section.getAttribute('id');
            const navLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
            
            if (scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
                navLinks.forEach(link => link.classList.remove('active'));
                if (navLink) navLink.classList.add('active');
            }
        });
    });
}

// ===== HERO SLIDER =====
function initHeroSlider() {
    const slides = document.querySelectorAll('.hero-slide');
    
    if (slides.length > 1) {
        setInterval(() => {
            slides[heroSlideIndex].classList.remove('active');
            heroSlideIndex = (heroSlideIndex + 1) % slides.length;
            slides[heroSlideIndex].classList.add('active');
        }, 5000);
    }
}

// ===== PORTFOLIO SECTIONS =====
function initPortfolioSections() {
    loadVideos();
    loadPhotos();
    loadGraphics();
    loadEvents();
    initFilters();
}

function loadVideos() {
    const videoGrid = document.getElementById('video-grid');
    if (!videoGrid) return;
    
    videoGrid.innerHTML = portfolioData.videos.map(video => `
        <div class="video-card" data-category="${video.category}">
            <div class="card-image">
                <img src="${video.thumbnail}" alt="${video.title}" loading="lazy">
                <div class="card-overlay">
                    <div class="play-button" onclick="openVideoModal('${video.videoUrl}')">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <h3 class="card-title">${video.title}</h3>
                <p class="card-description">${video.description}</p>
                <div class="card-meta">
                    <span class="card-category">${video.category}</span>
                    <span>HD Quality</span>
                </div>
            </div>
        </div>
    `).join('');
}

function loadPhotos() {
    const photoGrid = document.getElementById('photo-grid');
    if (!photoGrid) return;
    
    photoGrid.innerHTML = portfolioData.photos.map((photo, index) => `
        <div class="photo-card" data-category="${photo.category}" onclick="openLightbox(${index}, 'photos')">
            <div class="card-image">
                <img src="${photo.src}" alt="${photo.title}" loading="lazy">
                <div class="card-overlay">
                    <div class="play-button">
                        <i class="fas fa-search-plus"></i>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <h3 class="card-title">${photo.title}</h3>
                <span class="card-category">${photo.category}</span>
            </div>
        </div>
    `).join('');
}

function loadGraphics() {
    const graphicsGrid = document.getElementById('graphics-grid');
    if (!graphicsGrid) return;
    
    graphicsGrid.innerHTML = portfolioData.graphics.map((graphic, index) => `
        <div class="graphics-card" data-category="${graphic.category}" onclick="openLightbox(${index}, 'graphics')">
            <div class="card-image">
                <img src="${graphic.src}" alt="${graphic.title}" loading="lazy">
                <div class="card-overlay">
                    <div class="play-button">
                        <i class="fas fa-search-plus"></i>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <h3 class="card-title">${graphic.title}</h3>
                <span class="card-category">${graphic.category}</span>
            </div>
        </div>
    `).join('');
}

function loadEvents() {
    const eventsGrid = document.getElementById('events-grid');
    if (!eventsGrid) return;

    eventsGrid.innerHTML = portfolioData.events.map((event, index) => `
        <div class="event-card" onclick="openLightbox(${index}, 'events')">
            <div class="card-image">
                <img src="${event.src}" alt="${event.title}" loading="lazy">
                <div class="card-overlay">
                    <div class="play-button">
                        <i class="fas fa-search-plus"></i>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <h3 class="card-title">${event.title}</h3>
                <p class="card-description">${event.description}</p>
            </div>
        </div>
    `).join('');
}

// ===== FILTERS =====
function initFilters() {
    // Video filters
    const videoFilters = document.querySelectorAll('.video-filters .filter-btn');
    videoFilters.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.dataset.filter;
            filterItems('.video-card', filter, videoFilters, btn);
        });
    });

    // Photo filters
    const photoFilters = document.querySelectorAll('.photo-filters .filter-btn');
    photoFilters.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.dataset.filter;
            filterItems('.photo-card', filter, photoFilters, btn);
        });
    });
}

function filterItems(selector, filter, filterButtons, activeButton) {
    // Update active button
    filterButtons.forEach(btn => btn.classList.remove('active'));
    activeButton.classList.add('active');

    // Filter items
    const items = document.querySelectorAll(selector);
    items.forEach(item => {
        if (filter === 'all' || item.dataset.category === filter) {
            item.style.display = 'block';
            item.style.animation = 'fadeInUp 0.5s ease forwards';
        } else {
            item.style.display = 'none';
        }
    });
}

// ===== LIGHTBOX =====
function initLightbox() {
    const lightbox = document.getElementById('lightbox');
    const lightboxClose = document.querySelector('.lightbox-close');
    const lightboxPrev = document.getElementById('lightbox-prev');
    const lightboxNext = document.getElementById('lightbox-next');

    if (lightboxClose) {
        lightboxClose.addEventListener('click', closeLightbox);
    }

    if (lightboxPrev) {
        lightboxPrev.addEventListener('click', () => navigateLightbox(-1));
    }

    if (lightboxNext) {
        lightboxNext.addEventListener('click', () => navigateLightbox(1));
    }

    // Close lightbox on background click
    if (lightbox) {
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                closeLightbox();
            }
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (lightbox && lightbox.classList.contains('active')) {
            if (e.key === 'Escape') closeLightbox();
            if (e.key === 'ArrowLeft') navigateLightbox(-1);
            if (e.key === 'ArrowRight') navigateLightbox(1);
        }
    });
}

function openLightbox(index, type) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxCaption = document.getElementById('lightbox-caption');

    currentImageIndex = index;
    currentImages = portfolioData[type];

    if (lightbox && lightboxImage && lightboxCaption) {
        const item = currentImages[index];
        lightboxImage.src = item.src;
        lightboxCaption.textContent = item.title;
        lightbox.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    if (lightbox) {
        lightbox.classList.remove('active');
        document.body.style.overflow = 'auto';
    }
}

function navigateLightbox(direction) {
    currentImageIndex += direction;

    if (currentImageIndex < 0) {
        currentImageIndex = currentImages.length - 1;
    } else if (currentImageIndex >= currentImages.length) {
        currentImageIndex = 0;
    }

    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxCaption = document.getElementById('lightbox-caption');

    if (lightboxImage && lightboxCaption) {
        const item = currentImages[currentImageIndex];
        lightboxImage.src = item.src;
        lightboxCaption.textContent = item.title;
    }
}

// ===== VIDEO MODAL =====
function openVideoModal(videoUrl) {
    const modal = document.createElement('div');
    modal.className = 'video-modal';
    modal.innerHTML = `
        <div class="video-modal-content">
            <span class="video-modal-close">&times;</span>
            <iframe src="${videoUrl}" frameborder="0" allowfullscreen></iframe>
        </div>
    `;

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';

    // Close modal functionality
    const closeBtn = modal.querySelector('.video-modal-close');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
            document.body.style.overflow = 'auto';
        }
    });
}

// ===== CONTACT FORM =====
function initContactForm() {
    const contactForm = document.getElementById('contact-form');

    if (contactForm) {
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Get form data
            const formData = new FormData(contactForm);
            const data = Object.fromEntries(formData);

            // Simulate form submission
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            submitBtn.disabled = true;

            setTimeout(() => {
                // Show success message
                showNotification('Message sent successfully! I\'ll get back to you soon.', 'success');
                contactForm.reset();

                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    }
}

// ===== SCROLL EFFECTS =====
function initScrollEffects() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();

            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80;

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Parallax effect for hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero');

        if (hero) {
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        }
    });
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Hide notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 4000);
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', initLazyLoading);

// ===== FAQ FUNCTIONALITY =====
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', () => {
            // Close other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });

            // Toggle current item
            item.classList.toggle('active');
        });
    });
}

// ===== ENHANCED NAVIGATION FOR INDIVIDUAL PAGES =====
function initPageNavigation() {
    // Update active navigation link based on current page
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        }
    });
}

// ===== ENHANCED FORM HANDLING =====
function initEnhancedContactForm() {
    const contactForm = document.getElementById('contact-form');

    if (contactForm) {
        // Add real-time validation
        const inputs = contactForm.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            input.addEventListener('blur', validateField);
            input.addEventListener('input', clearFieldError);
        });

        contactForm.addEventListener('submit', handleFormSubmission);
    }
}

function validateField(e) {
    const field = e.target;
    const value = field.value.trim();

    // Remove existing error
    clearFieldError(e);

    // Validate based on field type
    let isValid = true;
    let errorMessage = '';

    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required';
    } else if (field.type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
    } else if (field.type === 'tel' && value && !isValidPhone(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid phone number';
    }

    if (!isValid) {
        showFieldError(field, errorMessage);
    }

    return isValid;
}

function clearFieldError(e) {
    const field = e.target;
    const formGroup = field.closest('.form-group');
    const existingError = formGroup.querySelector('.field-error');

    if (existingError) {
        existingError.remove();
    }

    formGroup.classList.remove('error');
}

function showFieldError(field, message) {
    const formGroup = field.closest('.form-group');
    formGroup.classList.add('error');

    const errorElement = document.createElement('span');
    errorElement.className = 'field-error';
    errorElement.textContent = message;

    formGroup.appendChild(errorElement);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

function handleFormSubmission(e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Validate all fields
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isFormValid = true;

    inputs.forEach(input => {
        if (!validateField({ target: input })) {
            isFormValid = false;
        }
    });

    if (!isFormValid) {
        showNotification('Please correct the errors in the form', 'error');
        return;
    }

    // Simulate form submission
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    submitBtn.disabled = true;

    setTimeout(() => {
        // Show success message
        showNotification('Message sent successfully! I\'ll get back to you within 24 hours.', 'success');
        form.reset();

        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Clear any existing errors
        const errors = form.querySelectorAll('.field-error');
        errors.forEach(error => error.remove());

        const errorGroups = form.querySelectorAll('.form-group.error');
        errorGroups.forEach(group => group.classList.remove('error'));

    }, 2000);
}

// ===== ADDITIONAL STYLES FOR DYNAMIC ELEMENTS =====
const additionalStyles = `
    .video-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    }

    .video-modal-content {
        position: relative;
        width: 90%;
        max-width: 800px;
        aspect-ratio: 16/9;
    }

    .video-modal-content iframe {
        width: 100%;
        height: 100%;
        border-radius: 10px;
    }

    .video-modal-close {
        position: absolute;
        top: -40px;
        right: 0;
        color: white;
        font-size: 2rem;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .video-modal-close:hover {
        color: var(--primary-color);
    }

    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        transform: translateX(400px);
        transition: transform 0.3s ease;
        z-index: 10002;
    }

    .notification.show {
        transform: translateX(0);
    }

    .notification-success {
        border-left: 4px solid #10b981;
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-primary);
    }

    .notification-content i {
        color: #10b981;
    }
`;

// Inject additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
