/* Professional Events Section Styles */
.events-section {
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

/* Category Filter */
.event-categories-pro {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 3rem;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.event-categories-pro.visible {
  opacity: 1;
  transform: translateY(0);
}

.category-button-pro {
  padding: 0.7rem 1.5rem;
  background-color: #fff;
  color: #333;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-size: 0.9rem;
}

.category-button-pro:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.category-button-pro.active {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 12px rgba(14, 59, 125, 0.2);
}

/* Events Grid */
.events-grid-pro {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.events-grid-pro.visible {
  opacity: 1;
  transform: translateY(0);
}

.event-card-pro {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  height: 100%;
  cursor: pointer;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
}

.event-card-pro.animated {
  opacity: 1;
  transform: translateY(0);
}

.event-card-pro:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.event-image-pro {
  height: 200px;
  background-size: cover;
  background-position: center;
  position: relative;
  transition: all 0.5s ease;
}

.event-date-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background-color: white;
  border-radius: 8px;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.event-card-pro:hover .event-date-badge {
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.event-date-month {
  font-size: 0.8rem;
  font-weight: 700;
  color: var(--primary-color);
  text-transform: uppercase;
}

.event-date-day {
  font-size: 1.2rem;
  font-weight: 800;
  color: #333;
}

.event-content-pro {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.event-header-pro {
  margin-bottom: 1rem;
}

.event-category-badge {
  display: inline-block;
  background-color: rgba(14, 59, 125, 0.1);
  color: var(--primary-color);
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  transition: all 0.3s ease;
}

.event-card-pro:hover .event-category-badge {
  background-color: var(--primary-color);
  color: white;
}

.event-title-pro {
  font-size: 1.3rem;
  color: #333;
  margin: 0 0 0.8rem 0;
  font-weight: 700;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.event-card-pro:hover .event-title-pro {
  color: var(--primary-color);
}

.event-details-pro {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.event-location-pro {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-location-pro i {
  color: var(--primary-color);
}

.event-tags-pro {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.event-tag-pro {
  background-color: #f0f0f0;
  color: #666;
  padding: 0.2rem 0.6rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.event-card-pro:hover .event-tag-pro {
  background-color: #e0e0e0;
}

.event-excerpt-pro {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.event-details-btn-pro {
  align-self: flex-start;
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-details-btn-pro:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.event-details-btn-pro i {
  transition: transform 0.3s ease;
}

.event-details-btn-pro:hover i {
  transform: translateX(3px);
}

/* Services Section */
.event-services-pro {
  margin-top: 5rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.event-services-pro.visible {
  opacity: 1;
  transform: translateY(0);
}

.services-grid-pro {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-card-pro {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  display: flex;
  padding: 2rem;
  gap: 1.5rem;
  align-items: flex-start;
}

.service-card-pro:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.service-icon-pro {
  width: 60px;
  height: 60px;
  background-color: rgba(14, 59, 125, 0.1);
  color: var(--primary-color);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.service-card-pro:hover .service-icon-pro {
  background-color: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.service-content-pro {
  flex-grow: 1;
}

.service-title-pro {
  font-size: 1.2rem;
  color: #333;
  margin: 0 0 0.8rem 0;
  font-weight: 700;
}

.service-description-pro {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.2rem;
}

.service-link-pro {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.service-link-pro:hover {
  color: var(--secondary-color);
}

.service-link-pro i {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.service-link-pro:hover i {
  transform: translateX(3px);
}

/* Event Modal */
.event-modal-pro {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

.event-modal-content-pro {
  background-color: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.5s forwards;
}

.modal-close-pro {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  background-color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  color: #333;
  font-size: 1rem;
}

.modal-close-pro:hover {
  background-color: #f0f0f0;
  transform: rotate(90deg);
}

.event-modal-header-pro {
  position: relative;
}

.event-modal-image-pro {
  height: 300px;
  overflow: hidden;
}

.event-modal-image-pro img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.event-modal-content-pro:hover .event-modal-image-pro img {
  transform: scale(1.05);
}

.event-modal-header-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.event-modal-meta-pro {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.event-modal-category-pro {
  background-color: var(--primary-color);
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.event-modal-date-pro {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.event-modal-date-pro::before {
  content: '\f073';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.event-modal-title-pro {
  font-size: 2rem;
  margin: 0 0 1rem 0;
  font-weight: 800;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.event-modal-location-pro {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.event-modal-tags-pro {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.event-modal-tag-pro {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  backdrop-filter: blur(5px);
}

.event-modal-body-pro {
  padding: 2rem;
}

.event-modal-section-pro {
  margin-bottom: 2.5rem;
}

.event-modal-section-title-pro {
  font-size: 1.4rem;
  color: #333;
  margin: 0 0 1.2rem 0;
  font-weight: 700;
  position: relative;
  padding-bottom: 0.8rem;
}

.event-modal-section-title-pro::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

.event-modal-description-pro {
  color: #666;
  font-size: 1rem;
  line-height: 1.7;
}

.event-modal-description-pro p {
  margin-bottom: 1rem;
}

.event-gallery-grid-pro {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.event-gallery-item-pro {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.event-gallery-item-pro:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.event-gallery-image-pro {
  width: 100%;
  height: 150px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.event-gallery-item-pro:hover .event-gallery-image-pro {
  transform: scale(1.1);
}

.event-highlights-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.event-highlights-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.8rem;
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
}

.event-highlights-list li::before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: var(--primary-color);
  position: absolute;
  left: 0;
  top: 2px;
}

.event-modal-cta-pro {
  text-align: center;
  margin-top: 3rem;
}

.event-modal-cta-button-pro {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  background-color: var(--primary-color);
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(14, 59, 125, 0.2);
}

.event-modal-cta-button-pro:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.3);
}

.event-modal-cta-button-pro i {
  transition: transform 0.3s ease;
}

.event-modal-cta-button-pro:hover i {
  transform: translateX(3px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .events-grid-pro {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .services-grid-pro {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .event-modal-header-content {
    position: relative;
    background: white;
    color: #333;
    padding: 1.5rem;
  }
  
  .event-modal-category-pro {
    background-color: var(--primary-color);
  }
  
  .event-modal-tag-pro {
    background-color: #f0f0f0;
    color: #666;
  }
  
  .event-modal-title-pro {
    text-shadow: none;
  }
}

@media (max-width: 768px) {
  .events-grid-pro {
    grid-template-columns: 1fr;
  }
  
  .services-grid-pro {
    grid-template-columns: 1fr;
  }
  
  .service-card-pro {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.5rem;
  }
  
  .service-link-pro {
    justify-content: center;
  }
  
  .event-modal-content-pro {
    max-height: 85vh;
  }
  
  .event-modal-image-pro {
    height: 200px;
  }
  
  .event-modal-title-pro {
    font-size: 1.5rem;
  }
  
  .event-gallery-grid-pro {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .category-button-pro {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .event-image-pro {
    height: 180px;
  }
  
  .event-title-pro {
    font-size: 1.2rem;
  }
  
  .event-modal-pro {
    padding: 1rem;
  }
  
  .event-modal-body-pro {
    padding: 1.5rem;
  }
  
  .event-modal-section-title-pro {
    font-size: 1.2rem;
  }
  
  .event-gallery-grid-pro {
    grid-template-columns: 1fr;
  }
  
  .event-modal-cta-button-pro {
    width: 100%;
    justify-content: center;
  }
}
