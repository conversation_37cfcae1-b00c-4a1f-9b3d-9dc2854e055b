import React, { useState, useEffect, useRef } from 'react';
import './Sections.css';
import './EventsSection.css';
import './RedesignedEventsSection.css';

const RedesignedEventsSection = () => {
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [activeCategory, setActiveCategory] = useState('All');
  const [animatedItems, setAnimatedItems] = useState([]);
  const sectionRef = useRef(null);

  // Enhanced event data with actual event images from the events folder
  const eventItems = [
    {
      id: 1,
      title: 'National Basketball Championship',
      date: 'June 15, 2024',
      location: 'National Stadium, Delhi',
      description: 'Comprehensive coverage of the national basketball championship finals, featuring the country\'s top teams competing for the prestigious title. This event includes pre-game analysis, live commentary, post-game interviews with players and coaches, and in-depth highlight packages.',
      imageUrl: '/images/sports/DSC08306-Enhanced-NR.jpg',
      category: 'Championship',
      tags: ['Live Coverage', 'Basketball', 'National'],
      gallery: [
        '/images/sports/DSC_1913.jpg',
        '/images/sports/DSC_4909-Enhanced-NR.jpg',
        '/images/sports/DSC_5152-Enhanced-NR.jpg',
        '/images/sports/DSC_5338-Enhanced-NR.jpg'
      ],
      highlights: [
        'Live play-by-play commentary throughout the championship game',
        'Exclusive interviews with MVP players and coaching staff',
        'Comprehensive statistical analysis and game breakdowns',
        'Behind-the-scenes footage of team preparations and celebrations'
      ]
    },
    {
      id: 2,
      title: 'International Cricket Series',
      date: 'July 22, 2024',
      location: 'Cricket Stadium, Mumbai',
      description: 'Full media coverage of the international cricket series between India and Australia, including match commentary, player interviews, and expert analysis. This high-profile event attracts global attention and features comprehensive digital and broadcast content production.',
      imageUrl: '/images/sports/DSC08558-Enhanced-NR.jpg',
      category: 'International',
      tags: ['Cricket', 'Series Coverage', 'International'],
      gallery: [
        '/images/sports/DSC_5376.jpg',
        '/images/sports/DSC_5608-Enhanced-NR.jpg',
        '/images/sports/DSC_5713.jpg',
        '/images/sports/750_0350-Enhanced-NR.jpg'
      ],
      highlights: [
        'Ball-by-ball commentary for all five test matches',
        'Daily recap shows with expert panel discussions',
        'Player profile features and career retrospectives',
        'Technical analysis segments with former international players'
      ]
    },
    {
      id: 3,
      title: 'College Football Tournament',
      date: 'August 10, 2024',
      location: 'University Stadium, Bangalore',
      description: 'Coverage of the annual inter-college football tournament featuring the top university teams from across the country. This event showcases emerging talent and includes feature stories on promising young athletes, team strategies, and the competitive collegiate sports landscape.',
      imageUrl: '/images/sports/DSC08928.jpg',
      category: 'Tournament',
      tags: ['Football', 'College Sports', 'Tournament'],
      gallery: [
        '/images/sports/750_0441-Enhanced-NR.jpg',
        '/images/sports/DSC00061-Enhanced-NR.jpg',
        '/images/sports/DSC00113-Enhanced-NR.jpg',
        '/images/sports/DSC00156-Enhanced-NR.jpg'
      ],
      highlights: [
        'Coverage of all 32 tournament matches across three weeks',
        'Special segments on rising stars and NFL prospects',
        'Tactical analysis of innovative coaching strategies',
        'Documentary-style features on team rivalries and traditions'
      ]
    },
    {
      id: 4,
      title: 'Olympic Trials Press Conference',
      date: 'September 5, 2024',
      location: 'Olympic Training Center, Delhi',
      description: 'Media coverage of the Olympic trials press conference, featuring interviews with athletes, coaches, and Olympic committee members. This event provides insights into the selection process, athlete preparations, and expectations for the upcoming Olympic Games.',
      imageUrl: '/images/sports/DSC09229-Enhanced-NR.jpg',
      category: 'Press Conference',
      tags: ['Olympics', 'Press Coverage', 'Interviews'],
      gallery: [
        '/images/sports/DSC00231-Enhanced-NR.jpg',
        '/images/sports/DSC00732-Enhanced-NR.jpg',
        '/images/sports/DSC04917-Enhanced-NR.jpg',
        '/images/sports/DSC05591-Enhanced-NR.jpg'
      ],
      highlights: [
        'Exclusive one-on-one interviews with Olympic hopefuls',
        'In-depth coverage of selection criteria and qualification standards',
        'Feature stories on athletes\' training regimens and personal journeys',
        'Expert analysis of India\'s medal prospects across various sports'
      ]
    },
    {
      id: 5,
      title: 'Regional Athletics Championship',
      date: 'October 18, 2024',
      location: 'Sports Complex, Chennai',
      description: 'Complete coverage of the regional athletics championship, featuring track and field events, swimming competitions, and team sports. This multi-day event showcases athletic talent from across the region and serves as a qualifier for national championships.',
      imageUrl: '/images/sports/DSC09584-Enhanced-NR.jpg',
      category: 'Championship',
      tags: ['Athletics', 'Regional', 'Multi-sport'],
      gallery: [
        '/images/sports/DSC05711-Enhanced-NR.jpg',
        '/images/sports/DSC06401-Enhanced-NR.jpg',
        '/images/sports/DSC06411-Enhanced-NR.jpg',
        '/images/sports/DSC07025.jpg'
      ],
      highlights: [
        'Live coverage of all final events across 12 sports disciplines',
        'Daily highlight packages and medal ceremony coverage',
        'Interviews with regional sports administrators and coaches',
        'Special features on community impact and sports development initiatives'
      ]
    },
    {
      id: 6,
      title: 'Sports Awards Ceremony',
      date: 'November 30, 2024',
      location: 'Grand Hotel, Delhi',
      description: 'Coverage of the annual sports awards ceremony honoring outstanding athletes, coaches, and teams from the past year. This prestigious event includes red carpet interviews, acceptance speeches, and special feature segments on the year\'s most memorable sporting moments.',
      imageUrl: '/images/sports/DSC09840-Enhanced-NR.jpg',
      category: 'Ceremony',
      tags: ['Awards', 'Gala', 'Recognition'],
      gallery: [
        '/images/sports/DSC07308-Enhanced-NR.jpg',
        '/images/sports/DSC07394-Enhanced-NR.jpg',
        '/images/sports/DSC08306-Enhanced-NR.jpg',
        '/images/sports/DSC08558-Enhanced-NR.jpg'
      ],
      highlights: [
        'Red carpet interviews with all major award nominees and presenters',
        'Live broadcast of the entire three-hour ceremony',
        'Behind-the-scenes access to preparation and rehearsals',
        'Retrospective segments celebrating the year\'s sporting achievements'
      ]
    }
  ];

  // Get unique categories
  const categories = ['All', ...new Set(eventItems.map(event => event.category))];

  // Filter events based on active category
  const filteredEvents = activeCategory === 'All'
    ? eventItems
    : eventItems.filter(event => event.category === activeCategory);

  // Open event details
  const openEventDetails = (event) => {
    setSelectedEvent(event);
    document.body.style.overflow = 'hidden';
  };

  // Close event details
  const closeEventDetails = () => {
    setSelectedEvent(null);
    document.body.style.overflow = 'auto';
  };

  // Handle category change
  const handleCategoryChange = (category) => {
    setActiveCategory(category);
    setAnimatedItems([]);

    // Reset animation state to trigger new animations
    setTimeout(() => {
      const newAnimatedItems = [];
      filteredEvents.forEach((_, index) => {
        setTimeout(() => {
          newAnimatedItems.push(index);
          setAnimatedItems([...newAnimatedItems]);
        }, 100 * index);
      });
    }, 50);
  };

  // Initialize animations when component mounts
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          // Start animations for initial events
          const newAnimatedItems = [];
          filteredEvents.forEach((_, index) => {
            setTimeout(() => {
              newAnimatedItems.push(index);
              setAnimatedItems([...newAnimatedItems]);
            }, 150 * index);
          });
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  // Update animations when filtered events change
  useEffect(() => {
    setAnimatedItems([]);

    // Reset animation state to trigger new animations
    setTimeout(() => {
      const newAnimatedItems = [];
      filteredEvents.forEach((_, index) => {
        setTimeout(() => {
          newAnimatedItems.push(index);
          setAnimatedItems([...newAnimatedItems]);
        }, 100 * index);
      });
    }, 50);
  }, [filteredEvents]);

  return (
    <section className="section events-section redesigned" id="events" ref={sectionRef}>
      <div className="events-hero">
        <div className="events-hero-content">
          <h1 className="events-hero-title">Event Coverage</h1>
          <p className="events-hero-subtitle">Professional sports event coverage across the country</p>
          <div className="events-hero-stats">
            <div className="hero-stat">
              <span className="stat-number">50+</span>
              <span className="stat-label">Events Covered</span>
            </div>
            <div className="hero-stat">
              <span className="stat-number">12</span>
              <span className="stat-label">Sports Categories</span>
            </div>
            <div className="hero-stat">
              <span className="stat-number">25+</span>
              <span className="stat-label">Venues</span>
            </div>
          </div>
        </div>
      </div>

      <div className="section-content">
        <div className="events-intro">
          <h2 className="section-title">Recent Event Coverage</h2>
          <p className="section-description">
            Explore my professional sports event coverage across the country. From championship games to press conferences,
            I deliver high-quality broadcasting and multimedia content that captures the excitement and stories of sporting events.
          </p>
        </div>

        {/* Event Category Filter */}
        <div className="events-categories">
          {categories.map((category) => (
            <button
              key={category}
              className={`category-button ${activeCategory === category ? 'active' : ''}`}
              onClick={() => handleCategoryChange(category)}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Events Grid */}
        <div className="events-grid">
          {filteredEvents.map((event, index) => (
            <div
              key={event.id}
              className={`event-card ${animatedItems.includes(index) ? 'animated' : ''}`}
              onClick={() => openEventDetails(event)}
            >
              <div className="event-card-inner">
                <div className="event-image" style={{ backgroundImage: `url(${event.imageUrl})` }}>
                  <div className="event-overlay">
                    <div className="event-date">
                      <span className="event-month">{event.date.split(' ')[0]}</span>
                      <span className="event-day">{event.date.split(' ')[1].replace(',', '')}</span>
                    </div>
                    <div className="event-category-tag">{event.category}</div>
                  </div>
                </div>
                <div className="event-content">
                  <h3 className="event-title">{event.title}</h3>
                  <p className="event-location">
                    <i className="fas fa-map-marker-alt"></i> {event.location}
                  </p>
                  <p className="event-excerpt">{event.description.substring(0, 120)}...</p>
                  <div className="event-tags">
                    {event.tags.map(tag => (
                      <span key={tag} className="event-tag">{tag}</span>
                    ))}
                  </div>
                  <button className="event-details-btn">
                    View Details <i className="fas fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Event Services */}
        <div className="event-services">
          <h2 className="section-subtitle">Event Coverage Services</h2>
          <div className="services-grid">
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-microphone"></i>
              </div>
              <h3 className="service-title">Live Broadcasting</h3>
              <p className="service-description">
                Professional live commentary and play-by-play coverage for sporting events of all sizes.
              </p>
              <a href="#contact" className="service-link">
                Learn More <i className="fas fa-chevron-right"></i>
              </a>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-camera"></i>
              </div>
              <h3 className="service-title">Event Photography</h3>
              <p className="service-description">
                High-quality sports photography capturing the key moments and emotions of your event.
              </p>
              <a href="#contact" className="service-link">
                Learn More <i className="fas fa-chevron-right"></i>
              </a>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-video"></i>
              </div>
              <h3 className="service-title">Video Production</h3>
              <p className="service-description">
                Complete video production services including highlight reels, interviews, and feature segments.
              </p>
              <a href="#contact" className="service-link">
                Learn More <i className="fas fa-chevron-right"></i>
              </a>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-newspaper"></i>
              </div>
              <h3 className="service-title">Media Coverage</h3>
              <p className="service-description">
                Comprehensive media coverage including press conferences, interviews, and written content.
              </p>
              <a href="#contact" className="service-link">
                Learn More <i className="fas fa-chevron-right"></i>
              </a>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="events-cta">
          <div className="cta-content">
            <h2 className="cta-title">Need Professional Coverage for Your Next Event?</h2>
            <p className="cta-description">
              Let's discuss how I can provide comprehensive media coverage for your upcoming sports event.
            </p>
            <a href="#contact" className="cta-button">
              Get in Touch <i className="fas fa-arrow-right"></i>
            </a>
          </div>
        </div>
      </div>

      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="event-modal" onClick={closeEventDetails}>
          <div className="event-modal-content" onClick={e => e.stopPropagation()}>
            <button className="modal-close-btn" onClick={closeEventDetails}>
              <i className="fas fa-times"></i>
            </button>

            <div className="event-modal-header">
              <div className="modal-image-container">
                <img src={selectedEvent.imageUrl} alt={selectedEvent.title} className="modal-image" />
                <div className="modal-image-overlay">
                  <div className="modal-category">{selectedEvent.category}</div>
                  <h2 className="modal-title">{selectedEvent.title}</h2>
                  <div className="modal-meta">
                    <span className="modal-date"><i className="fas fa-calendar-alt"></i> {selectedEvent.date}</span>
                    <span className="modal-location"><i className="fas fa-map-marker-alt"></i> {selectedEvent.location}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="event-modal-body">
              <div className="modal-section">
                <h3 className="modal-section-title">Event Description</h3>
                <p className="modal-description">{selectedEvent.description}</p>
              </div>

              <div className="modal-section">
                <h3 className="modal-section-title">Coverage Highlights</h3>
                <ul className="modal-highlights">
                  {selectedEvent.highlights.map((highlight, index) => (
                    <li key={index} className="highlight-item">
                      <i className="fas fa-check-circle"></i> {highlight}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="modal-section">
                <h3 className="modal-section-title">Event Gallery</h3>
                <div className="modal-gallery">
                  {selectedEvent.gallery.map((image, index) => (
                    <div key={index} className="gallery-item">
                      <img src={image} alt={`${selectedEvent.title} - Image ${index + 1}`} className="gallery-image" />
                    </div>
                  ))}
                </div>
              </div>

              <div className="modal-tags">
                {selectedEvent.tags.map(tag => (
                  <span key={tag} className="modal-tag">{tag}</span>
                ))}
              </div>

              <div className="modal-cta">
                <a href="#contact" className="modal-cta-button">
                  Request Similar Coverage <i className="fas fa-arrow-right"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default RedesignedEventsSection;
