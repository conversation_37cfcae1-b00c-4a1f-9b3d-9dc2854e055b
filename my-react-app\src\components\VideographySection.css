/* Videography Section Styles */
.videography-section {
  background-color: var(--light-color);
  position: relative;
  overflow: hidden;
}

.videography-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.03;
  z-index: -1;
}

/* Featured Video Section */
.featured-video-section {
  margin: 3rem auto 5rem;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
  max-width: 1000px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

.featured-video-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.featured-video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  color: white;
}

.featured-video-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.featured-video-badge {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  padding: 0.5rem 1.2rem;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.featured-video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
}

.featured-video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.featured-video-content {
  padding: 2rem;
}

.featured-video-name {
  font-size: 1.8rem;
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-weight: 700;
}

.featured-video-description {
  color: var(--gray-color);
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.featured-video-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--gray-color);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  font-weight: 500;
}

.meta-item i {
  color: var(--primary-color);
}

.featured-video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.video-tag {
  background-color: var(--gray-light);
  color: var(--primary-color);
  padding: 0.4rem 0.8rem;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.video-tag:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Video Categories */
.video-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.category-button {
  padding: 0.8rem 1.5rem;
  background-color: white;
  color: #333;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.category-name {
  position: relative;
  z-index: 2;
}

.category-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 8px;
  background-color: #f0f0f0;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 700;
  color: #555;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.category-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  z-index: 1;
}

.category-button:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.category-button:hover .category-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.category-button:hover::before {
  opacity: 1;
}

.category-button.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.25);
}

.category-button.active .category-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Video Grid */
.video-featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2.5rem;
  margin: 3rem 0;
}

.video-card {
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp 0.8s forwards;
  animation-delay: calc(var(--animation-order) * 0.1s);
}

.video-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 16px 16px 0 0;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  z-index: 0;
}

.video-content {
  padding: 1.8rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.video-date {
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
}

.video-duration {
  color: var(--gray-color);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.video-title {
  font-size: 1.4rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.video-card:hover .video-title {
  color: var(--primary-color);
}

.video-description {
  color: var(--gray-color);
  font-size: 1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.video-tags {
  display: flex;
  gap: 0.8rem;
  margin-bottom: 1.5rem;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 1.2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  color: var(--gray-color);
  font-size: 0.95rem;
  margin-bottom: 1.2rem;
}

.video-views, .video-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.video-views i, .video-category i {
  color: var(--primary-color);
  opacity: 0.7;
}

.set-featured-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 5px 15px rgba(14, 59, 125, 0.2);
  margin-top: auto;
}

.set-featured-btn:hover {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.3);
}

/* Video Services */
.video-services {
  margin-top: 5rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2.5rem;
}

.service-card {
  background-color: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp 0.8s forwards;
  animation-delay: calc(var(--animation-order) * 0.1s);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 2rem;
  box-shadow: 0 10px 25px rgba(14, 59, 125, 0.2);
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(10deg);
  box-shadow: 0 15px 35px rgba(14, 59, 125, 0.3);
}

.service-title {
  font-size: 1.3rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 700;
  transition: color 0.3s ease;
}

.service-card:hover .service-title {
  color: var(--primary-color);
}

.service-description {
  color: var(--gray-color);
  font-size: 1rem;
  line-height: 1.7;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .video-featured-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 992px) {
  .video-featured-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .featured-video-name {
    font-size: 1.6rem;
  }
  
  .featured-video-meta {
    gap: 1rem;
  }
  
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .video-featured-grid {
    grid-template-columns: 1fr;
  }
  
  .featured-video-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .featured-video-meta {
    flex-direction: column;
    gap: 0.8rem;
  }
  
  .video-categories {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 1rem;
    justify-content: flex-start;
  }
  
  .category-button {
    flex: 0 0 auto;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .featured-video-section {
    margin: 2rem auto 3rem;
  }
  
  .featured-video-content {
    padding: 1.5rem;
  }
  
  .featured-video-name {
    font-size: 1.4rem;
  }
  
  .video-content {
    padding: 1.5rem;
  }
  
  .video-title {
    font-size: 1.2rem;
  }
  
  .service-card {
    padding: 1.5rem;
  }
  
  .service-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}
