import React from 'react';
import { Link } from 'react-router-dom';
import '../styles/FuturisticTheme.css';

const FuturisticFooter = () => {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer style={{
      backgroundColor: 'var(--futuristic-dark-alt)',
      color: 'var(--futuristic-text-secondary)',
      borderTop: 'var(--futuristic-border)',
      position: 'relative',
      overflow: 'hidden',
      padding: '4rem 2rem 2rem'
    }}>
      {/* Decorative elements */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '2px',
        background: 'var(--futuristic-gradient)',
        opacity: 0.8
      }}></div>
      
      <div style={{
        position: 'absolute',
        top: '2px',
        left: '50%',
        transform: 'translateX(-50%)',
        width: '150px',
        height: '3px',
        background: 'var(--futuristic-accent)',
        borderRadius: '0 0 3px 3px',
        boxShadow: 'var(--futuristic-glow)'
      }}></div>

      {/* Back to top button */}
      <div style={{
        position: 'absolute',
        top: '-25px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 10
      }}>
        <button 
          onClick={scrollToTop} 
          aria-label="Back to top"
          style={{
            width: '50px',
            height: '50px',
            borderRadius: '50%',
            background: 'var(--futuristic-gradient)',
            color: 'white',
            border: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            boxShadow: 'var(--futuristic-glow)',
            transition: 'all 0.3s ease'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = 'var(--futuristic-glow-red)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = 'var(--futuristic-glow)';
          }}
        >
          <i className="fas fa-chevron-up"></i>
        </button>
      </div>

      {/* Footer content */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '3rem',
        position: 'relative',
        zIndex: 1
      }}>
        {/* Logo and description */}
        <div>
          <h3 style={{
            color: 'var(--futuristic-text)',
            fontSize: '1.8rem',
            marginBottom: '1rem',
            fontWeight: '700',
            position: 'relative',
            display: 'inline-block'
          }}>
            Siddartha Manubothu
            <span style={{
              position: 'absolute',
              bottom: '-5px',
              left: '0',
              width: '40px',
              height: '3px',
              background: 'var(--futuristic-accent)',
              borderRadius: '2px'
            }}></span>
          </h3>
          <p style={{
            color: 'var(--futuristic-text-secondary)',
            lineHeight: '1.7',
            marginBottom: '1.5rem'
          }}>
            Professional sports broadcaster and multimedia journalist with a passion for capturing the excitement and drama of sports through various media formats.
          </p>
        </div>

        {/* Navigation */}
        <div>
          <h4 style={{
            color: 'var(--futuristic-text)',
            fontSize: '1.2rem',
            marginBottom: '1.2rem',
            fontWeight: '600'
          }}>Portfolio</h4>
          <ul style={{
            listStyle: 'none',
            padding: 0,
            margin: 0
          }}>
            {[
              { path: '/', label: 'Dashboard' },
              { path: '/broadcast', label: 'On-Air' },
              { path: '/photography', label: 'Photography' },
              { path: '/videography', label: 'Videography' },
              { path: '/graphics', label: 'Graphic Design' },
              { path: '/events', label: 'Events' },
              { path: '/contact', label: 'Contact' }
            ].map((item, index) => (
              <li key={index} style={{ marginBottom: '0.8rem' }}>
                <Link 
                  to={item.path}
                  style={{
                    color: 'var(--futuristic-text-secondary)',
                    textDecoration: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.color = 'var(--futuristic-accent-alt)';
                    e.currentTarget.style.paddingLeft = '5px';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.color = 'var(--futuristic-text-secondary)';
                    e.currentTarget.style.paddingLeft = '0';
                  }}
                >
                  <i className="fas fa-chevron-right" style={{ fontSize: '0.7rem' }}></i>
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Contact */}
        <div>
          <h4 style={{
            color: 'var(--futuristic-text)',
            fontSize: '1.2rem',
            marginBottom: '1.2rem',
            fontWeight: '600'
          }}>Contact</h4>
          <div style={{ marginBottom: '1.5rem' }}>
            <p style={{ marginBottom: '0.8rem', display: 'flex', alignItems: 'center', gap: '0.8rem' }}>
              <i className="fas fa-envelope" style={{ color: 'var(--futuristic-accent)' }}></i>
              <EMAIL>
            </p>
            <p style={{ display: 'flex', alignItems: 'center', gap: '0.8rem' }}>
              <i className="fas fa-phone" style={{ color: 'var(--futuristic-accent)' }}></i>
              (*************
            </p>
          </div>
          <div style={{
            display: 'flex',
            gap: '1rem'
          }}>
            {[
              { icon: 'fab fa-linkedin-in', label: 'LinkedIn' },
              { icon: 'fab fa-twitter', label: 'Twitter' },
              { icon: 'fab fa-instagram', label: 'Instagram' },
              { icon: 'fab fa-youtube', label: 'YouTube' }
            ].map((social, index) => (
              <a 
                key={index}
                href="#" 
                aria-label={social.label}
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  background: 'rgba(255, 255, 255, 0.05)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'var(--futuristic-text)',
                  border: 'var(--futuristic-border)',
                  transition: 'all 0.3s ease'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.background = 'var(--futuristic-gradient)';
                  e.currentTarget.style.boxShadow = 'var(--futuristic-glow)';
                  e.currentTarget.style.transform = 'translateY(-3px)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <i className={social.icon}></i>
              </a>
            ))}
          </div>
        </div>
      </div>

      {/* Footer bottom */}
      <div style={{
        borderTop: 'var(--futuristic-border)',
        marginTop: '3rem',
        paddingTop: '2rem',
        textAlign: 'center'
      }}>
        <p style={{ marginBottom: '0.5rem' }}>&copy; {new Date().getFullYear()} Siddartha Manubothu. All rights reserved.</p>
        <p style={{ color: 'var(--futuristic-accent)', fontWeight: '600' }}>Sports Broadcaster & Multimedia Journalist</p>
      </div>
    </footer>
  );
};

export default FuturisticFooter;
