import React, { useState, useEffect, useRef } from 'react';
import './Sections.css';
import './PhotographySection.css';

const FixedPhotographySection = () => {
  const [activeCategory, setActiveCategory] = useState('All');
  const [animatedItems, setAnimatedItems] = useState([]);
  const filterRef = useRef(null);
  const masonryRef = useRef(null);

  // Sports images with all available images from the sports folder
  const sportsImages = [
    {
      id: 1,
      title: 'Basketball Intensity',
      category: 'Action Shots',
      description: 'A pivotal moment captured during the championship basketball game, showcasing the athleticism and determination that defines elite sports competition.',
      imageUrl: '/images/sports/750_0350-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R5',
        lens: '70-200mm f/2.8',
        settings: 'f/2.8, 1/1000s, ISO 1600',
        location: 'National Basketball Arena'
      }
    },
    {
      id: 2,
      title: 'Football Determination',
      category: 'Action Shots',
      description: 'The raw intensity and focus displayed by athletes during a crucial football match, where split-second decisions can determine the outcome of the game.',
      imageUrl: '/images/sports/750_0441-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A1',
        lens: '400mm f/2.8 GM',
        settings: 'f/3.2, 1/2000s, ISO 800',
        location: 'Memorial Stadium'
      }
    },
    {
      id: 3,
      title: 'Stadium Atmosphere',
      category: 'Game Day',
      description: 'The electric atmosphere of a packed stadium on game day, capturing the collective energy of thousands of passionate fans united in support of their team.',
      imageUrl: '/images/sports/DSC00732-Enhanced-NR.jpg',
      metadata: {
        camera: 'Nikon Z9',
        lens: '24-70mm f/2.8',
        settings: 'f/4, 1/500s, ISO 400',
        location: 'Riverside Arena'
      }
    },
    {
      id: 4,
      title: 'Game-Changing Play',
      category: 'Action Shots',
      description: 'The decisive moment that changed the course of the game, frozen in time to showcase the skill and precision that separates champions from competitors.',
      imageUrl: '/images/sports/DSC04917-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R3',
        lens: '300mm f/2.8',
        settings: 'f/2.8, 1/1600s, ISO 1250',
        location: 'Victory Field'
      }
    },
    {
      id: 5,
      title: 'Athletic Grace',
      category: 'Action Shots',
      description: 'The perfect balance of grace and power as an athlete executes a complex maneuver, demonstrating years of training and natural talent in a single frame.',
      imageUrl: '/images/sports/DSC05591-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A9 II',
        lens: '135mm f/1.8 GM',
        settings: 'f/2.0, 1/2500s, ISO 640',
        location: 'Olympic Training Center'
      }
    },
    {
      id: 6,
      title: 'Championship Celebration',
      category: 'Championship Moments',
      description: 'The unbridled joy of victory as the team celebrates their hard-earned championship, a culmination of countless hours of preparation and sacrifice.',
      imageUrl: '/images/sports/DSC05711-Enhanced-NR.jpg',
      metadata: {
        camera: 'Nikon Z8',
        lens: '70-200mm f/2.8',
        settings: 'f/3.5, 1/800s, ISO 1000',
        location: 'Championship Stadium'
      }
    },
    {
      id: 7,
      title: 'Sideline Strategy',
      category: 'Behind the Scenes',
      description: 'The intense concentration on the sidelines as coaches and players strategize during a critical timeout, showcasing the mental aspect of athletic competition.',
      imageUrl: '/images/sports/DSC06401-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R5',
        lens: '24-70mm f/2.8',
        settings: 'f/3.2, 1/500s, ISO 2000',
        location: 'University Arena'
      }
    },
    {
      id: 8,
      title: 'Fan Passion',
      category: 'Game Day',
      description: 'The passionate reaction of fans during a crucial moment, their emotions mirroring the drama unfolding on the field and adding to the electric atmosphere of game day.',
      imageUrl: '/images/sports/DSC06411-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A7R IV',
        lens: '70-200mm f/2.8 GM',
        settings: 'f/3.5, 1/640s, ISO 1250',
        location: 'National Stadium'
      }
    },
    {
      id: 9,
      title: 'Athletic Focus',
      category: 'Action Shots',
      description: 'The intense concentration and determination etched on an athlete\'s face, revealing the mental fortitude required to perform at the highest level of competition.',
      imageUrl: '/images/sports/DSC07025.jpg',
      metadata: {
        camera: 'Nikon Z9',
        lens: '400mm f/2.8',
        settings: 'f/2.8, 1/2000s, ISO 800',
        location: 'Elite Sports Complex'
      }
    },
    {
      id: 10,
      title: 'Trophy Moment',
      category: 'Championship Moments',
      description: 'The culmination of a season\'s worth of hard work and dedication captured in the moment a team lifts the championship trophy, etching their names in history.',
      imageUrl: '/images/sports/DSC07308-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS 1DX Mark III',
        lens: '85mm f/1.4',
        settings: 'f/2.0, 1/1000s, ISO 1600',
        location: 'Championship Arena'
      }
    },
    {
      id: 11,
      title: 'Team Unity',
      category: 'Behind the Scenes',
      description: 'The camaraderie and team spirit that forms the foundation of athletic success, captured in a candid moment of connection between teammates.',
      imageUrl: '/images/sports/DSC07394-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A1',
        lens: '35mm f/1.4 GM',
        settings: 'f/1.8, 1/500s, ISO 1250',
        location: 'Team Training Facility'
      }
    },
    {
      id: 12,
      title: 'Crowd Atmosphere',
      category: 'Game Day',
      description: 'The collective energy of thousands of fans creating an unforgettable atmosphere that elevates the game and inspires athletes to reach new heights of performance.',
      imageUrl: '/images/sports/DSC08306-Enhanced-NR.jpg',
      metadata: {
        camera: 'Nikon Z8',
        lens: '14-24mm f/2.8',
        settings: 'f/4, 1/320s, ISO 2000',
        location: 'Grand Stadium'
      }
    },
    {
      id: 13,
      title: 'Victory Emotion',
      category: 'Championship Moments',
      description: 'The raw emotion of victory after a hard-fought contest, capturing the release of pressure and the pure joy that comes with achieving a long-sought goal.',
      imageUrl: '/images/sports/DSC08558-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R6',
        lens: '135mm f/1.8',
        settings: 'f/2.0, 1/1000s, ISO 1250',
        location: 'Championship Field'
      }
    },
    {
      id: 14,
      title: 'Pre-Game Preparation',
      category: 'Behind the Scenes',
      description: 'The focused preparation before the spotlight shines, showing the meticulous routines athletes follow to prepare themselves mentally and physically for competition.',
      imageUrl: '/images/sports/DSC08928.jpg',
      metadata: {
        camera: 'Sony A7 IV',
        lens: '24-70mm f/2.8 GM',
        settings: 'f/2.8, 1/250s, ISO 3200',
        location: 'Team Locker Room'
      }
    },
    {
      id: 15,
      title: 'Defining Athletic Moment',
      category: 'Action Shots',
      description: 'A split-second of athletic brilliance that defines a career, showcasing the perfect combination of skill, timing, and competitive instinct.',
      imageUrl: '/images/sports/DSC09229-Enhanced-NR.jpg',
      metadata: {
        camera: 'Nikon Z9',
        lens: '300mm f/2.8',
        settings: 'f/2.8, 1/2500s, ISO 1000',
        location: 'Premier Sports Arena'
      }
    },
    {
      id: 16,
      title: 'Courtside Intensity',
      category: 'Action Shots',
      description: 'The intensity of competition captured from a unique courtside perspective, showing the raw athleticism and determination of elite athletes.',
      imageUrl: '/images/sports/DSC09584-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R3',
        lens: '24-70mm f/2.8',
        settings: 'f/2.8, 1/1250s, ISO 1600',
        location: 'Downtown Arena'
      }
    },
    {
      id: 17,
      title: 'Championship Drive',
      category: 'Action Shots',
      description: 'The determination and focus required to drive to the basket against elite defenders, showcasing both physical prowess and mental toughness.',
      imageUrl: '/images/sports/DSC09840-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A1',
        lens: '70-200mm f/2.8 GM',
        settings: 'f/2.8, 1/1600s, ISO 2000',
        location: 'Championship Court'
      }
    },
    {
      id: 18,
      title: 'Decisive Moment',
      category: 'Action Shots',
      description: 'The split-second that can determine the outcome of a game, frozen in time to showcase the athleticism and skill that defines elite sports.',
      imageUrl: '/images/sports/DSC_1913.jpg',
      metadata: {
        camera: 'Nikon Z9',
        lens: '400mm f/2.8',
        settings: 'f/2.8, 1/2000s, ISO 1250',
        location: 'National Sports Arena'
      }
    },
    {
      id: 19,
      title: 'Athletic Excellence',
      category: 'Action Shots',
      description: 'The perfect form and technique that comes from years of dedicated training, captured in a moment of pure athletic excellence.',
      imageUrl: '/images/sports/DSC_4909-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R5',
        lens: '300mm f/2.8',
        settings: 'f/2.8, 1/2500s, ISO 800',
        location: 'Elite Training Center'
      }
    },
    {
      id: 20,
      title: 'Team Celebration',
      category: 'Championship Moments',
      description: 'The shared joy of victory that strengthens team bonds and creates lasting memories for both athletes and fans alike.',
      imageUrl: '/images/sports/DSC_5152-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A7R IV',
        lens: '24-70mm f/2.8 GM',
        settings: 'f/3.5, 1/800s, ISO 1600',
        location: 'Victory Stadium'
      }
    },
    {
      id: 21,
      title: 'Competitive Spirit',
      category: 'Action Shots',
      description: 'The competitive spirit that drives athletes to push beyond their limits and achieve greatness in the pursuit of victory.',
      imageUrl: '/images/sports/DSC_5338-Enhanced-NR.jpg',
      metadata: {
        camera: 'Nikon Z8',
        lens: '70-200mm f/2.8',
        settings: 'f/2.8, 1/1250s, ISO 1000',
        location: 'Championship Court'
      }
    },
    {
      id: 22,
      title: 'Game-Winning Moment',
      category: 'Championship Moments',
      description: 'The exhilaration of scoring the game-winning point, a moment of triumph that represents the culmination of countless hours of preparation and dedication.',
      imageUrl: '/images/sports/DSC_5376.jpg',
      metadata: {
        camera: 'Canon EOS 1DX Mark III',
        lens: '400mm f/2.8',
        settings: 'f/2.8, 1/2000s, ISO 2000',
        location: 'Finals Arena'
      }
    },
    {
      id: 23,
      title: 'Athletic Determination',
      category: 'Action Shots',
      description: 'The unwavering determination that separates champions from competitors, captured in a moment of intense physical and mental effort.',
      imageUrl: '/images/sports/DSC_5608-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A1',
        lens: '135mm f/1.8 GM',
        settings: 'f/2.0, 1/1600s, ISO 1250',
        location: 'Premier Sports Complex'
      }
    },
    {
      id: 24,
      title: 'Championship Defense',
      category: 'Action Shots',
      description: 'The defensive intensity that can be just as crucial as offensive prowess in determining the outcome of championship games.',
      imageUrl: '/images/sports/DSC_5713.jpg',
      metadata: {
        camera: 'Nikon Z9',
        lens: '70-200mm f/2.8',
        settings: 'f/2.8, 1/1250s, ISO 1600',
        location: 'Championship Court'
      }
    },
    {
      id: 25,
      title: 'Courtside Action',
      category: 'Action Shots',
      description: 'The fast-paced action of basketball captured from a unique courtside perspective, showcasing the speed and agility of elite athletes.',
      imageUrl: '/images/sports/750_0350-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R3',
        lens: '24-70mm f/2.8',
        settings: 'f/2.8, 1/1000s, ISO 2000',
        location: 'Downtown Arena'
      }
    },
    {
      id: 26,
      title: 'Championship Focus',
      category: 'Action Shots',
      description: 'The intense focus required to perform at the highest level when the pressure is at its peak during championship competition.',
      imageUrl: '/images/sports/750_0441-Enhanced-NR.jpg',
      metadata: {
        camera: 'Sony A1',
        lens: '400mm f/2.8 GM',
        settings: 'f/2.8, 1/2000s, ISO 1600',
        location: 'Championship Stadium'
      }
    },
    {
      id: 27,
      title: 'Athletic Precision',
      category: 'Action Shots',
      description: 'The precision and control that comes from years of dedicated practice, captured in a moment of perfect athletic execution.',
      imageUrl: '/images/sports/DSC00061-Enhanced-NR.jpg',
      metadata: {
        camera: 'Nikon Z8',
        lens: '70-200mm f/2.8',
        settings: 'f/2.8, 1/1600s, ISO 1250',
        location: 'Elite Sports Arena'
      }
    },
    {
      id: 28,
      title: 'Competitive Intensity',
      category: 'Action Shots',
      description: 'The intensity of competition that brings out the best in athletes, pushing them to achieve feats that seemed impossible during practice.',
      imageUrl: '/images/sports/DSC00113-Enhanced-NR.jpg',
      metadata: {
        camera: 'Canon EOS R5',
        lens: '300mm f/2.8',
        settings: 'f/2.8, 1/2000s, ISO 1600',
        location: 'Championship Court'
      }
    }
  ];

  // Add "All" category and get unique categories with counts
  const uniqueCategories = [...new Set(sportsImages.map(img => img.category))];
  const categoryCounts = {};

  // Count images in each category
  sportsImages.forEach(img => {
    if (categoryCounts[img.category]) {
      categoryCounts[img.category]++;
    } else {
      categoryCounts[img.category] = 1;
    }
  });

  // Create categories array with "All" first
  const categories = [
    { name: 'All', count: sportsImages.length },
    ...uniqueCategories.map(cat => ({ name: cat, count: categoryCounts[cat] }))
  ];

  // Filter images based on active category
  const filteredImages = activeCategory === 'All'
    ? sportsImages
    : sportsImages.filter(img => img.category === activeCategory);

  // Simple useEffect to animate items
  useEffect(() => {
    // Animate all items with a slight delay
    filteredImages.forEach((_, index) => {
      setTimeout(() => {
        setAnimatedItems(prev => [...prev, index]);
      }, 50 * index);
    });
  }, [filteredImages]);

  return (
    <section className="section photography-section" id="photography">
      <div className="photography-intro">
        <h2 className="animated-title">Sports Photography</h2>
        <div className="title-accent"></div>
        <p className="animated-text">
          Freezing the decisive moments that define athletic excellence. My sports photography portfolio captures the
          raw intensity, emotion, and beauty of competitive sports through a professional lens that tells the complete story.
        </p>
        <div className="intro-badges">
          <div className="intro-badge">
            <i className="fas fa-camera"></i>
            <span>Professional Equipment</span>
          </div>
          <div className="intro-badge">
            <i className="fas fa-bolt"></i>
            <span>Action Shots</span>
          </div>
          <div className="intro-badge">
            <i className="fas fa-trophy"></i>
            <span>Championship Moments</span>
          </div>
          <div className="intro-badge">
            <i className="fas fa-users"></i>
            <span>Team Spirit</span>
          </div>
        </div>
      </div>

      {/* Featured Image Showcase */}
      <div className="featured-photo-showcase">
        <div className="featured-photo-container">
          <img
            src="/images/sports/DSC05711-Enhanced-NR.jpg"
            alt="Featured Sports Photography"
            className="featured-photo"
          />
          <div className="featured-photo-overlay">
            <div className="featured-photo-content">
              <span className="featured-photo-label">Featured Work</span>
              <h3 className="featured-photo-title">Championship Celebration</h3>
              <p className="featured-photo-description">
                The unbridled joy of victory as the team celebrates their hard-earned championship,
                a culmination of countless hours of preparation and sacrifice.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="photo-filter" ref={filterRef}>
        {categories.map((category, index) => (
          <button
            key={category.name}
            className={`filter-button ${activeCategory === category.name ? 'active' : ''}`}
            onClick={() => setActiveCategory(category.name)}
            style={{ animationDelay: `${0.1 * index}s` }}
          >
            <span className="filter-name">{category.name}</span>
            <span className="filter-count">{category.count}</span>
          </button>
        ))}
      </div>

      {/* Photo Counter */}
      <div className="photo-counter">
        Showing <span>{filteredImages.length}</span> photos {activeCategory !== 'All' && `in ${activeCategory}`}
      </div>

      {/* Masonry Photo Grid */}
      <div className="photo-masonry" ref={masonryRef}>
        {filteredImages.map((image, index) => (
          <div
            key={image.id}
            className={`photo-item ${animatedItems.includes(index) ? 'animated' : ''}`}
            style={{ animationDelay: `${0.05 * index}s` }}
          >
            <div className="photo-item-inner">
              <img
                src={image.imageUrl}
                alt={image.title}
                className="photo-image"
                loading="lazy"
              />
              <div className="photo-info">
                <span className="photo-category">{image.category}</span>
                <h3 className="photo-title">{image.title}</h3>
                <p className="photo-description">{image.description.substring(0, 80)}...</p>
              </div>
              <div className="photo-hover-effect"></div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default FixedPhotographySection;
