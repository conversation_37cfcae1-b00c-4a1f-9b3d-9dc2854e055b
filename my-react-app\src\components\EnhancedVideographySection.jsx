import React, { useState, useEffect, useRef } from 'react';
import './Sections.css';
import './VideographySection.css';
import '../styles/FuturisticTheme.css';
import VideographyHero from './VideographyHero';

const EnhancedVideographySection = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [featuredVideo, setFeaturedVideo] = useState(null);
  const sectionRef = useRef(null);

  // Updated YouTube videos with the provided links
  const featuredVideos = [
    {
      id: 1,
      title: 'Siddartha Manubothu - Sports Reporting Demo Reel',
      category: 'highlights',
      description: 'A showcase of my sports reporting work covering various sporting events and interviews with athletes.',
      videoUrl: 'https://www.youtube.com/embed/3T766dyF-Dc',
      date: 'June 15, 2023',
      views: '8.7K',
      duration: '3:45',
      tags: ['sports reporting', 'demo reel', 'broadcast journalism']
    },
    {
      id: 2,
      title: '<PERSON><PERSON><PERSON> - On-Air Talent Demo',
      category: 'highlights',
      description: 'Highlights of my on-air broadcasting work including live game coverage and studio analysis.',
      videoUrl: 'https://www.youtube.com/embed/JhRjDT5KNRY',
      date: 'May 22, 2023',
      views: '5.2K',
      duration: '4:12',
      tags: ['on-air talent', 'broadcasting', 'sports analysis']
    },
    {
      id: 3,
      title: 'Siddartha Manubothu - Sports Broadcasting Reel',
      category: 'broadcast',
      description: 'Professional sports broadcasting reel showcasing play-by-play commentary, analysis, and on-camera presence during live sporting events.',
      videoUrl: 'https://www.youtube.com/embed/G1kwEMIKl2w',
      date: 'April 10, 2023',
      views: '12.3K',
      duration: '5:18',
      tags: ['sports broadcasting', 'play-by-play', 'commentary']
    },
    {
      id: 4,
      title: 'Siddartha Manubothu - Sports Journalism Portfolio',
      category: 'journalism',
      description: 'A comprehensive look at my sports journalism work, featuring interviews, field reporting, and in-studio segments across various sporting events.',
      videoUrl: 'https://www.youtube.com/embed/nj7Ob0flGF4',
      date: 'March 28, 2023',
      views: '7.5K',
      duration: '6:42',
      tags: ['sports journalism', 'interviews', 'field reporting']
    },
    {
      id: 5,
      title: 'Basketball Championship Coverage',
      category: 'broadcast',
      description: 'Full coverage of the championship basketball game with play-by-play commentary and post-game analysis.',
      videoUrl: 'https://www.youtube.com/embed/3T766dyF-Dc',
      date: 'July 5, 2023',
      views: '15.3K',
      duration: '8:22',
      tags: ['basketball', 'championship', 'live coverage']
    },
    {
      id: 6,
      title: 'Football Season Highlights',
      category: 'highlights',
      description: 'A compilation of the best moments from the football season, featuring key plays and game-changing moments.',
      videoUrl: 'https://www.youtube.com/embed/JhRjDT5KNRY',
      date: 'August 12, 2023',
      views: '22.1K',
      duration: '7:15',
      tags: ['football', 'highlights', 'season recap']
    },
    {
      id: 7,
      title: 'Athlete Interview Series: Rising Stars',
      category: 'journalism',
      description: 'In-depth interviews with emerging athletic talents, exploring their journeys, challenges, and aspirations in the world of sports.',
      videoUrl: 'https://www.youtube.com/embed/G1kwEMIKl2w',
      date: 'September 3, 2023',
      views: '9.8K',
      duration: '10:30',
      tags: ['interviews', 'rising stars', 'athlete profiles']
    },
    {
      id: 8,
      title: 'Behind the Scenes: Game Day Production',
      category: 'journalism',
      description: 'An exclusive look at what goes into producing a professional sports broadcast, from pre-game preparation to post-game wrap-up.',
      videoUrl: 'https://www.youtube.com/embed/nj7Ob0flGF4',
      date: 'October 18, 2023',
      views: '11.5K',
      duration: '12:45',
      tags: ['behind the scenes', 'production', 'game day']
    },
    {
      id: 9,
      title: 'Cricket Match Analysis - IPL Finals',
      category: 'broadcast',
      description: 'In-depth analysis of the recent cricket match between Mumbai Indians and Chennai Super Kings in the IPL finals.',
      videoUrl: 'https://www.youtube.com/embed/G1kwEMIKl2w',
      date: 'November 5, 2023',
      views: '18.2K',
      duration: '9:30',
      tags: ['cricket', 'IPL', 'match analysis']
    },
    {
      id: 10,
      title: 'Football Pre-Game Show - National Championship',
      category: 'broadcast',
      description: 'Hosting the pre-game show for the national football championship with expert insights and predictions.',
      videoUrl: 'https://www.youtube.com/embed/3T766dyF-Dc',
      date: 'December 15, 2023',
      views: '25.7K',
      duration: '11:20',
      tags: ['football', 'pre-game', 'championship']
    },
    {
      id: 11,
      title: 'Sports Documentary: The Road to Glory',
      category: 'journalism',
      description: 'A documentary following a team\'s journey through the season, capturing the highs, lows, and everything in between.',
      videoUrl: 'https://www.youtube.com/embed/JhRjDT5KNRY',
      date: 'January 20, 2024',
      views: '14.9K',
      duration: '15:45',
      tags: ['documentary', 'team journey', 'sports story']
    },
    {
      id: 12,
      title: 'Athlete Profile: Rising Basketball Star',
      category: 'journalism',
      description: 'An in-depth profile of an emerging basketball talent, exploring their background, training regimen, and future aspirations.',
      videoUrl: 'https://www.youtube.com/embed/nj7Ob0flGF4',
      date: 'February 8, 2024',
      views: '10.3K',
      duration: '8:15',
      tags: ['athlete profile', 'basketball', 'rising star']
    }
  ];

  useEffect(() => {
    // Set the first video as featured when component mounts
    setFeaturedVideo(featuredVideos[0]);

    // Add intersection observer for animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => observer.observe(el));
    }

    return () => {
      if (sectionRef.current) {
        const elements = sectionRef.current.querySelectorAll('.animate-on-scroll');
        elements.forEach((el) => observer.unobserve(el));
      }
    };
  }, []);

  // Video categories with counts
  const getCategoryCounts = () => {
    const counts = {};
    featuredVideos.forEach(video => {
      if (counts[video.category]) {
        counts[video.category]++;
      } else {
        counts[video.category] = 1;
      }
    });
    return counts;
  };

  const categoryCounts = getCategoryCounts();

  const categories = [
    { id: 'all', label: 'All Videos', count: featuredVideos.length },
    { id: 'highlights', label: 'Highlights', count: categoryCounts['highlights'] || 0 },
    { id: 'broadcast', label: 'Broadcasting', count: categoryCounts['broadcast'] || 0 },
    { id: 'journalism', label: 'Journalism', count: categoryCounts['journalism'] || 0 }
  ];

  // Filter videos based on active category
  const filteredVideos = activeCategory === 'all'
    ? featuredVideos
    : featuredVideos.filter(video => video.category === activeCategory);

  // Set a video as featured
  const handleSetFeatured = (video) => {
    setFeaturedVideo(video);
    // Scroll to featured video section
    document.getElementById('featured-video-section').scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="futuristic-section futuristic-bg" id="videography" ref={sectionRef}>
      <VideographyHero />

      {/* Featured Video Section */}
      <div id="featured-video-section" className="futuristic-featured animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
        {featuredVideo && (
          <>
            <div className="futuristic-featured-header">
              <h3 className="futuristic-featured-title"><i className="fas fa-play-circle"></i> Featured Video</h3>
              <div className="futuristic-featured-badge">
                <i className="fas fa-star"></i> Editor's Pick
              </div>
            </div>
            <div className="futuristic-video-container">
              <iframe
                src={featuredVideo.videoUrl}
                title={featuredVideo.title}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            </div>
            <div className="futuristic-card-content">
              <h3 className="futuristic-card-title">{featuredVideo.title}</h3>
              <p className="futuristic-card-description">{featuredVideo.description}</p>
              <div className="futuristic-tags">
                {featuredVideo.tags.map((tag, index) => (
                  <span key={index} className="futuristic-tag"><i className="fas fa-hashtag"></i> {tag}</span>
                ))}
              </div>
              <div className="futuristic-meta">
                <span className="futuristic-meta-item"><i className="fas fa-calendar-alt"></i> {featuredVideo.date}</span>
                <span className="futuristic-meta-item"><i className="fas fa-eye"></i> {featuredVideo.views} views</span>
                <span className="futuristic-meta-item"><i className="fas fa-clock"></i> {featuredVideo.duration}</span>
                <span className="futuristic-meta-item"><i className="fas fa-tag"></i> {featuredVideo.category}</span>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Video Categories */}
      <div className="video-categories animate-fadeInUp" style={{ animationDelay: '0.4s', marginTop: '3rem', display: 'flex', justifyContent: 'center', gap: '1rem', flexWrap: 'wrap' }}>
        {categories.map((category, index) => (
          <button
            key={category.id}
            className={`futuristic-button ${activeCategory === category.id ? 'active' : ''}`}
            onClick={() => setActiveCategory(category.id)}
            style={{
              background: activeCategory === category.id ? 'var(--futuristic-gradient)' : 'rgba(255, 255, 255, 0.1)',
              boxShadow: activeCategory === category.id ? 'var(--futuristic-glow)' : 'none',
              opacity: activeCategory === category.id ? 1 : 0.8
            }}
          >
            <span>{category.label}</span>
            <span style={{
              background: 'rgba(255, 255, 255, 0.2)',
              padding: '0.2rem 0.6rem',
              borderRadius: '50px',
              fontSize: '0.8rem',
              marginLeft: '0.5rem'
            }}>{category.count}</span>
          </button>
        ))}
      </div>

      {/* Video Grid */}
      <div className="futuristic-grid animate-fadeInUp" style={{ animationDelay: '0.5s' }}>
        {filteredVideos.map((video, index) => (
          <div key={video.id} className="futuristic-card" style={{ animationDelay: `${0.1 * index + 0.5}s` }}>
            <div className="futuristic-video-container">
              <iframe
                src={video.videoUrl}
                title={video.title}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            </div>
            <div className="futuristic-card-content">
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.8rem' }}>
                <span style={{ color: 'var(--futuristic-accent)', fontWeight: '600' }}>{video.date}</span>
                <span style={{ color: 'var(--futuristic-text-secondary)' }}><i className="fas fa-clock"></i> {video.duration}</span>
              </div>
              <h3 className="futuristic-card-title">{video.title}</h3>
              <p className="futuristic-card-description">{video.description}</p>
              <div className="futuristic-tags">
                {video.tags.slice(0, 2).map((tag, index) => (
                  <span key={index} className="futuristic-tag"><i className="fas fa-hashtag"></i> {tag}</span>
                ))}
              </div>
              <div className="futuristic-meta">
                <span className="futuristic-meta-item"><i className="fas fa-eye"></i> {video.views}</span>
                <span className="futuristic-meta-item"><i className="fas fa-tag"></i> {video.category}</span>
              </div>
              <button
                className="futuristic-button"
                onClick={() => handleSetFeatured(video)}
                style={{ width: '100%', marginTop: '1.5rem' }}
              >
                <i className="fas fa-star"></i> Set as Featured
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Video Production Services */}
      <div className="futuristic-services animate-fadeInUp" style={{ animationDelay: '0.6s' }}>
        <h3 className="futuristic-subtitle">Video Production Services</h3>
        <div className="futuristic-services-grid">
          <div className="futuristic-service-card" style={{ animationDelay: '0.7s' }}>
            <div className="futuristic-service-icon">
              <i className="fas fa-video"></i>
            </div>
            <h4 className="futuristic-service-title">Game Coverage</h4>
            <p className="futuristic-service-description">
              Professional coverage of sporting events with multiple camera angles and dynamic editing.
            </p>
          </div>
          <div className="futuristic-service-card" style={{ animationDelay: '0.8s' }}>
            <div className="futuristic-service-icon">
              <i className="fas fa-film"></i>
            </div>
            <h4 className="futuristic-service-title">Highlight Reels</h4>
            <p className="futuristic-service-description">
              Compelling highlight packages that showcase the best moments and plays.
            </p>
          </div>
          <div className="futuristic-service-card" style={{ animationDelay: '0.9s' }}>
            <div className="futuristic-service-icon">
              <i className="fas fa-user"></i>
            </div>
            <h4 className="futuristic-service-title">Athlete Profiles</h4>
            <p className="futuristic-service-description">
              In-depth video profiles that tell the stories of athletes and their journeys.
            </p>
          </div>
          <div className="futuristic-service-card" style={{ animationDelay: '1s' }}>
            <div className="futuristic-service-icon">
              <i className="fas fa-camera"></i>
            </div>
            <h4 className="futuristic-service-title">Cinematic Sports Films</h4>
            <p className="futuristic-service-description">
              Artistic sports films with cinematic quality and storytelling.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnhancedVideographySection;
