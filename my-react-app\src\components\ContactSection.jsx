import React, { useState } from 'react';
import './Sections.css';

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [formStatus, setFormStatus] = useState({
    submitted: false,
    success: false,
    message: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [activeField, setActiveField] = useState(null);
  const [charCount, setCharCount] = useState(0);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));

    // Update character count for message field
    if (name === 'message') {
      setCharCount(value.length);
    }

    // Clear errors when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }

    if (!formData.subject.trim()) {
      errors.subject = 'Subject is required';
    }

    if (!formData.message.trim()) {
      errors.message = 'Message is required';
    } else if (formData.message.length < 10) {
      errors.message = 'Message must be at least 10 characters';
    }

    return errors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setFormStatus({
        submitted: true,
        success: true,
        message: 'Thank you for your message! I will get back to you within 24 hours.'
      });

      // Reset form after successful submission
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
      setCharCount(0);
      setIsLoading(false);
    }, 2000);
  };

  const handleFocus = (fieldName) => {
    setActiveField(fieldName);
  };

  const handleBlur = () => {
    setActiveField(null);
  };

  return (
    <section className="section contact-section" id="contact">
      <h2 className="section-title">Get In Touch</h2>
      <div className="section-content">
        <p className="section-description">
          Interested in working together? Whether you need a sports broadcaster, photographer, videographer, or graphic designer for your next project, I'd love to hear from you.
        </p>
        
        <div className="contact-container">
          <div className="contact-info">
            <div className="contact-card">
              <div className="contact-icon">
                <i className="fas fa-envelope"></i>
              </div>
              <h3>Email</h3>
              <p><EMAIL></p>
            </div>
            
            <div className="contact-card">
              <div className="contact-icon">
                <i className="fas fa-phone"></i>
              </div>
              <h3>Phone</h3>
              <p>(*************</p>
            </div>
            
            <div className="contact-card">
              <div className="contact-icon">
                <i className="fas fa-map-marker-alt"></i>
              </div>
              <h3>Location</h3>
              <p>New York, NY</p>
            </div>
            
            <div className="contact-social">
              <h3>Connect With Me</h3>
              <div className="social-icons">
                <a href="#" className="social-icon" aria-label="LinkedIn">
                  <i className="fab fa-linkedin-in"></i>
                </a>
                <a href="#" className="social-icon" aria-label="Twitter">
                  <i className="fab fa-twitter"></i>
                </a>
                <a href="#" className="social-icon" aria-label="Instagram">
                  <i className="fab fa-instagram"></i>
                </a>
                <a href="#" className="social-icon" aria-label="YouTube">
                  <i className="fab fa-youtube"></i>
                </a>
              </div>
            </div>
          </div>
          
          <div className="contact-form-container">
            {formStatus.submitted && formStatus.success ? (
              <div className="form-success-message">
                <div className="success-icon">
                  <i className="fas fa-check-circle"></i>
                </div>
                <h3>Message Sent!</h3>
                <p>{formStatus.message}</p>
              </div>
            ) : (
              <form className="contact-form interactive-form" onSubmit={handleSubmit}>
                <div className={`form-group ${activeField === 'name' ? 'focused' : ''} ${formErrors.name ? 'error' : ''}`}>
                  <label htmlFor="name">
                    <i className="fas fa-user"></i> Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    onFocus={() => handleFocus('name')}
                    onBlur={handleBlur}
                    placeholder="Enter your full name"
                    required
                  />
                  {formErrors.name && <span className="error-message">{formErrors.name}</span>}
                </div>

                <div className={`form-group ${activeField === 'email' ? 'focused' : ''} ${formErrors.email ? 'error' : ''}`}>
                  <label htmlFor="email">
                    <i className="fas fa-envelope"></i> Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    onFocus={() => handleFocus('email')}
                    onBlur={handleBlur}
                    placeholder="Enter your email address"
                    required
                  />
                  {formErrors.email && <span className="error-message">{formErrors.email}</span>}
                </div>

                <div className={`form-group ${activeField === 'subject' ? 'focused' : ''} ${formErrors.subject ? 'error' : ''}`}>
                  <label htmlFor="subject">
                    <i className="fas fa-tag"></i> Subject *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    onFocus={() => handleFocus('subject')}
                    onBlur={handleBlur}
                    required
                  >
                    <option value="">Select a subject</option>
                    <option value="Broadcasting Services">Broadcasting Services</option>
                    <option value="Photography Services">Photography Services</option>
                    <option value="Videography Services">Videography Services</option>
                    <option value="Graphic Design Services">Graphic Design Services</option>
                    <option value="Event Coverage">Event Coverage</option>
                    <option value="General Inquiry">General Inquiry</option>
                    <option value="Collaboration">Collaboration Opportunity</option>
                  </select>
                  {formErrors.subject && <span className="error-message">{formErrors.subject}</span>}
                </div>

                <div className={`form-group ${activeField === 'message' ? 'focused' : ''} ${formErrors.message ? 'error' : ''}`}>
                  <label htmlFor="message">
                    <i className="fas fa-comment"></i> Message *
                    <span className="char-count">{charCount}/500</span>
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    onFocus={() => handleFocus('message')}
                    onBlur={handleBlur}
                    rows="5"
                    maxLength="500"
                    placeholder="Tell me about your project or inquiry..."
                    required
                  ></textarea>
                  {formErrors.message && <span className="error-message">{formErrors.message}</span>}
                </div>

                <button
                  type="submit"
                  className={`submit-button ${isLoading ? 'loading' : ''}`}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <i className="fas fa-spinner fa-spin"></i> Sending...
                    </>
                  ) : (
                    <>
                      Send Message <i className="fas fa-paper-plane"></i>
                    </>
                  )}
                </button>

                <div className="form-footer">
                  <p>
                    <i className="fas fa-shield-alt"></i>
                    Your information is secure and will never be shared.
                  </p>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
