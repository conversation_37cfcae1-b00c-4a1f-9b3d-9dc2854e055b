import { lazy, Suspense } from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import './App.css'
import Header from './components/Header'
import Navigation from './components/Navigation'

// Lazy load pages for better performance
const DashboardPage = lazy(() => import('./pages/DashboardPage'))

const PhotographyPage = lazy(() => import('./pages/PhotographyPage'))
const VideographyPage = lazy(() => import('./pages/VideographyPage'))
const GraphicDesignPage = lazy(() => import('./pages/GraphicDesignPage'))
const EventsPage = lazy(() => import('./pages/EventsPage'))
const ContactPage = lazy(() => import('./pages/ContactPage'))

// Loading component
const PageLoader = () => (
  <div className="page-loader">
    <div className="loader-spinner"></div>
    <p>Loading page...</p>
  </div>
)

function App() {
  return (
    <BrowserRouter>
      <div className="app">
        <Header />
        <Navigation />
        <Suspense fallback={<PageLoader />}>
          <Routes>
            <Route path="/" element={<DashboardPage />} />

            <Route path="/photography" element={<PhotographyPage />} />
            <Route path="/videography" element={<VideographyPage />} />
            <Route path="/graphics" element={<GraphicDesignPage />} />
            <Route path="/events" element={<EventsPage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </div>
    </BrowserRouter>
  )
}

export default App
