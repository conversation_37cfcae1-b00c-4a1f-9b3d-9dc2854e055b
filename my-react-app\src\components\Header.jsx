import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import './Header.css';

const Header = () => {
  const [isVisible, setIsVisible] = useState(false);
  // Removed mouse position state for professional static design
  const headerRef = useRef(null);
  const particlesRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const taglineRef = useRef(null);
  const badgesRef = useRef(null);
  const ctaRef = useRef(null);
  const profileRef = useRef(null);

  useEffect(() => {
    // Add animation effect when component mounts with staggered timing
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    // Create particles for the dynamic background
    if (particlesRef.current) {
      createParticles();
    }

    // Removed mouse tracking for professional static design

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Create particles for dynamic background
  const createParticles = () => {
    const particlesContainer = particlesRef.current;
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';

      // Random position, size, and animation delay
      const size = Math.random() * 5 + 2;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;
      particle.style.animationDelay = `${Math.random() * 5}s`;
      particle.style.animationDuration = `${Math.random() * 10 + 10}s`;

      particlesContainer.appendChild(particle);
    }
  };

  const scrollToNextSection = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: 'smooth'
    });
  };

  // Removed parallax effect for professional static design
  const getParallaxStyle = () => {
    return {}; // Return empty object to prevent movement
  };

  return (
    <header className="header" id="home" ref={headerRef}>
      <div className="header-particles" ref={particlesRef}></div>
      <div className="header-overlay"></div>
      <div className="header-gradient"></div>

      <div className="header-content">
        <div
          className={`header-profile-image ${isVisible ? 'visible' : ''}`}
          ref={profileRef}
          style={getParallaxStyle(15)}
        >
          <div className="profile-image-container">
            <img src="/images/profile/siddu.jpg" alt="Siddartha Manubothu broadcasting from a sports field" />
            <div className="profile-image-glow"></div>
          </div>
          <div className="profile-accent"></div>
        </div>

        <div className={`header-text-container ${isVisible ? 'visible' : ''}`}>
          <h1
            className="main-title text-gradient"
            data-text="Sports Portfolio"
            ref={titleRef}
            style={getParallaxStyle(10)}
          >
            <span className="title-highlight">Sports</span> Portfolio
            <div className="title-underline"></div>
          </h1>

          <h2
            className="subtitle"
            ref={subtitleRef}
            style={getParallaxStyle(20)}
          >
            Siddartha Manubothu
          </h2>

          <p
            className="tagline"
            ref={taglineRef}
            style={getParallaxStyle(25)}
          >
            Sports Broadcaster & Multimedia Journalist
          </p>

          <div
            className="header-badges"
            ref={badgesRef}
            style={getParallaxStyle(30)}
          >
            <span className="header-badge">
              <i className="fas fa-microphone"></i>
              <span>On-Air Talent</span>
            </span>
            <span className="header-badge">
              <i className="fas fa-video"></i>
              <span>Event Coverage</span>
            </span>
            <span className="header-badge">
              <i className="fas fa-camera"></i>
              <span>Multimedia</span>
            </span>
          </div>

          <div
            className="header-cta"
            ref={ctaRef}
            style={getParallaxStyle(35)}
          >
            <Link to="/broadcast" className="cta-button primary ripple">
              <span>View My Work</span>
              <i className="fas fa-arrow-right"></i>
              <div className="button-glow"></div>
            </Link>
            <Link to="/contact" className="cta-button secondary ripple">
              <span>Contact Me</span>
              <i className="fas fa-envelope"></i>
              <div className="button-glow"></div>
            </Link>
          </div>
        </div>
      </div>

      <div className="scroll-down-container">
        <button
          className="scroll-down-button pulse"
          onClick={scrollToNextSection}
          aria-label="Scroll down"
        >
          <i className="fas fa-chevron-down"></i>
          <div className="scroll-ring"></div>
        </button>
      </div>

      <div className="header-accent"></div>
      <div className="header-bottom-wave">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
          <path fill="#f1faee" fillOpacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
      </div>
    </header>
  );
};

export default Header;
