import React, { useState, useEffect, useRef } from 'react';
import './Sections.css';
import './EventsSection.css';

const EventsSection = () => {
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [animatedCards, setAnimatedCards] = useState([]);
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const descriptionRef = useRef(null);
  const categoriesRef = useRef(null);
  const eventsGridRef = useRef(null);
  const servicesRef = useRef(null);
  const serviceCardsRef = useRef(Array(4).fill(null));

  // Event data with images
  const eventItems = [
    {
      id: 1,
      title: 'Sports Championship Coverage',
      date: 'June 15, 2024',
      location: 'National Stadium, Delhi',
      description: 'Comprehensive coverage of the national sports championship, including interviews with athletes, game highlights, and behind-the-scenes footage.',
      imageUrl: '/images/events/event1.jpg',
      category: 'Championship',
      tags: ['Live Coverage', 'Interviews', 'Highlights']
    },
    {
      id: 2,
      title: 'Cricket Tournament Media Day',
      date: 'May 22, 2024',
      location: 'Cricket Stadium, Mumbai',
      description: 'Media day coverage for the international cricket tournament, featuring team introductions, press conferences, and pre-tournament analysis.',
      imageUrl: '/images/events/event2.jpg',
      category: 'Cricket',
      tags: ['Press Conference', 'Analysis', 'Interviews']
    },
    {
      id: 3,
      title: 'Football League Opening Ceremony',
      date: 'April 10, 2024',
      location: 'City Football Ground, Bangalore',
      description: 'Coverage of the opening ceremony for the national football league, including team presentations, entertainment performances, and inaugural match.',
      imageUrl: '/images/events/event3.jpg',
      category: 'Football',
      tags: ['Ceremony', 'Live Broadcast', 'Match Coverage']
    },
    {
      id: 4,
      title: 'Basketball Tournament Finals',
      date: 'March 28, 2024',
      location: 'Indoor Stadium, Chennai',
      description: 'Live coverage of the basketball tournament finals, with play-by-play commentary, post-game interviews, and trophy ceremony.',
      imageUrl: '/images/events/event4.jpg',
      category: 'Basketball',
      tags: ['Finals', 'Commentary', 'Trophy Ceremony']
    },
    {
      id: 5,
      title: 'Tennis Open Press Conference',
      date: 'February 14, 2024',
      location: 'Tennis Complex, Hyderabad',
      description: 'Coverage of the pre-tournament press conference for the Tennis Open, featuring player interviews and tournament preview.',
      imageUrl: '/images/events/event5.jpg',
      category: 'Tennis',
      tags: ['Press Conference', 'Interviews', 'Preview']
    },
    {
      id: 6,
      title: 'Olympic Qualifier Event',
      date: 'January 30, 2024',
      location: 'Sports Authority Complex, Pune',
      description: 'Coverage of the Olympic qualifier events, including athlete performances, qualification announcements, and interviews with coaches.',
      imageUrl: '/images/events/event6.jpg',
      category: 'Olympics',
      tags: ['Qualifiers', 'Interviews', 'Live Coverage']
    }
  ];

  // Get unique categories
  const categories = ['All', ...new Set(eventItems.map(event => event.category))];

  // State for active category filter and search
  const [activeCategory, setActiveCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  // Filter events based on active category and search term
  const filteredEvents = eventItems.filter(event => {
    const matchesCategory = activeCategory === 'All' || event.category === activeCategory;
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Open event details
  const openEventDetails = (event) => {
    setSelectedEvent(event);
    document.body.style.overflow = 'hidden';
  };

  // Close event details
  const closeEventDetails = () => {
    setSelectedEvent(null);
    document.body.style.overflow = 'auto';
  };

  // Handle category change with animation
  const handleCategoryChange = (category) => {
    setAnimatedCards([]);
    setActiveCategory(category);

    // Reset animation state to trigger new animations
    setTimeout(() => {
      const newAnimatedCards = [];
      filteredEvents.forEach((_, index) => {
        setTimeout(() => {
          newAnimatedCards.push(index);
          setAnimatedCards([...newAnimatedCards]);
        }, 100 * index);
      });
    }, 50);
  };

  // Intersection Observer for animations
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const handleIntersection = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      });
    };

    const observer = new IntersectionObserver(handleIntersection, observerOptions);

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  // Initialize card animations
  useEffect(() => {
    if (isVisible) {
      const newAnimatedCards = [];
      filteredEvents.forEach((_, index) => {
        setTimeout(() => {
          newAnimatedCards.push(index);
          setAnimatedCards([...newAnimatedCards]);
        }, 150 * index);
      });
    }
  }, [isVisible, filteredEvents]);

  // Service icons with animated properties
  const serviceIcons = [
    { icon: "fas fa-microphone", color: "#e63946", animation: "pulse" },
    { icon: "fas fa-camera", color: "#0e3b7d", animation: "float" },
    { icon: "fas fa-video", color: "#06d6a0", animation: "rotate" },
    { icon: "fas fa-newspaper", color: "#ffd166", animation: "pulse" }
  ];

  return (
    <section className="section events-section" id="events" ref={sectionRef}>
      <h2
        className={`section-title ${isVisible ? 'visible' : ''}`}
        ref={titleRef}
        data-text="Event Coverage"
      >
        Event Coverage
      </h2>

      <div className={`section-content ${isVisible ? 'visible' : ''}`}>
        <p
          className={`section-description ${isVisible ? 'visible' : ''}`}
          ref={descriptionRef}
        >
          Explore my professional sports event coverage across the country. From championship games to press conferences,
          I deliver high-quality broadcasting and multimedia content that captures the excitement and stories of sporting events.
        </p>

        {/* Interactive Controls */}
        <div className="events-interactive-controls">
          {/* Search Bar */}
          <div className="search-container">
            <div className="search-input-wrapper">
              <i className="fas fa-search search-icon"></i>
              <input
                type="text"
                placeholder="Search events by title, location, or tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
              {searchTerm && (
                <button
                  className="clear-search"
                  onClick={() => setSearchTerm('')}
                >
                  <i className="fas fa-times"></i>
                </button>
              )}
            </div>
          </div>

          {/* Results Info */}
          <div className="results-info">
            <span className="results-count">
              {filteredEvents.length} events found
              {activeCategory !== 'All' && ` in ${activeCategory}`}
              {searchTerm && ` matching "${searchTerm}"`}
            </span>
          </div>
        </div>

        {/* Event Category Filter */}
        <div
          className={`event-categories-pro ${isVisible ? 'visible' : ''}`}
          ref={categoriesRef}
        >
          {categories.map((category, index) => (
            <button
              key={category}
              className={`category-button-pro ${activeCategory === category ? 'active' : ''}`}
              onClick={() => handleCategoryChange(category)}
            >
              <span className="category-text">{category}</span>
              <span className="category-count">
                {category === 'All'
                  ? eventItems.length
                  : eventItems.filter(e => e.category === category).length
                }
              </span>
            </button>
          ))}
        </div>

        {/* Events Grid */}
        <div
          className={`events-grid-pro ${isVisible ? 'visible' : ''}`}
          ref={eventsGridRef}
        >
          {filteredEvents.map((event, index) => (
            <div
              key={event.id}
              className={`event-card-pro ${animatedCards.includes(index) ? 'animated' : ''}`}
              onClick={() => openEventDetails(event)}
            >
              <div
                className="event-image-pro"
                style={{ backgroundImage: `url(${event.imageUrl})` }}
              >
                <div className="event-date-badge">
                  <span className="event-date-month">{event.date.split(' ')[0]}</span>
                  <span className="event-date-day">{event.date.split(' ')[1].replace(',', '')}</span>
                </div>
              </div>
              <div className="event-content-pro">
                <div className="event-header-pro">
                  <span className="event-category-badge">{event.category}</span>
                  <h3 className="event-title-pro">{event.title}</h3>
                </div>
                <div className="event-details-pro">
                  <p className="event-location-pro">
                    <i className="fas fa-map-marker-alt"></i> {event.location}
                  </p>
                  <div className="event-tags-pro">
                    {event.tags.map(tag => (
                      <span key={tag} className="event-tag-pro">{tag}</span>
                    ))}
                  </div>
                  <p className="event-excerpt-pro">{event.description.substring(0, 120)}...</p>
                  <button className="event-details-btn-pro">
                    View Details <i className="fas fa-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Event Coverage Services */}
        <div className={`event-services-pro ${isVisible ? 'visible' : ''}`} ref={servicesRef}>
          <h3 className="section-subtitle">Professional Event Coverage Services</h3>
          <div className="services-grid-pro">
            {[
              {
                title: "Live Broadcasting",
                description: "Professional live broadcasting services for sporting events with real-time commentary and analysis.",
                icon: "fas fa-microphone"
              },
              {
                title: "Event Photography",
                description: "High-quality photography services capturing the key moments and atmosphere of sporting events.",
                icon: "fas fa-camera"
              },
              {
                title: "Video Production",
                description: "Complete video production services for event highlights, interviews, and promotional content.",
                icon: "fas fa-video"
              },
              {
                title: "Media Coverage",
                description: "Comprehensive media coverage including press conferences, interviews, and written content.",
                icon: "fas fa-newspaper"
              }
            ].map((service, index) => (
              <div
                className="service-card-pro"
                key={index}
                ref={el => serviceCardsRef.current[index] = el}
              >
                <div className="service-icon-pro">
                  <i className={service.icon}></i>
                </div>
                <div className="service-content-pro">
                  <h4 className="service-title-pro">{service.title}</h4>
                  <p className="service-description-pro">
                    {service.description}
                  </p>
                  <a href="#contact" className="service-link-pro">
                    Request Service <i className="fas fa-chevron-right"></i>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="event-modal-pro" onClick={closeEventDetails}>
          <div className="event-modal-content-pro" onClick={e => e.stopPropagation()}>
            <button className="modal-close-pro" onClick={closeEventDetails}>
              <i className="fas fa-times"></i>
            </button>

            <div className="event-modal-header-pro">
              <div className="event-modal-image-pro">
                <img src={selectedEvent.imageUrl} alt={selectedEvent.title} />
              </div>
              <div className="event-modal-header-content">
                <div className="event-modal-meta-pro">
                  <span className="event-modal-category-pro">{selectedEvent.category}</span>
                  <span className="event-modal-date-pro">{selectedEvent.date}</span>
                </div>
                <h2 className="event-modal-title-pro">{selectedEvent.title}</h2>
                <p className="event-modal-location-pro">
                  <i className="fas fa-map-marker-alt"></i> {selectedEvent.location}
                </p>
                <div className="event-modal-tags-pro">
                  {selectedEvent.tags.map(tag => (
                    <span key={tag} className="event-modal-tag-pro">{tag}</span>
                  ))}
                </div>
              </div>
            </div>

            <div className="event-modal-body-pro">
              <div className="event-modal-section-pro">
                <h3 className="event-modal-section-title-pro">Event Description</h3>
                <div className="event-modal-description-pro">
                  <p>{selectedEvent.description}</p>
                  <p>As a professional sports broadcaster, I provided comprehensive coverage of this event, delivering high-quality content across multiple platforms. The coverage included live commentary, post-event analysis, and exclusive interviews with key participants.</p>
                </div>
              </div>

              <div className="event-modal-section-pro">
                <h3 className="event-modal-section-title-pro">Event Gallery</h3>
                <div className="event-gallery-grid-pro">
                  {[1, 2, 3, 4].map(num => (
                    <div key={num} className="event-gallery-item-pro">
                      <img
                        src={`/images/events/event${num}.jpg`}
                        alt={`${selectedEvent.title} gallery ${num}`}
                        className="event-gallery-image-pro"
                      />
                    </div>
                  ))}
                </div>
              </div>

              <div className="event-modal-section-pro">
                <h3 className="event-modal-section-title-pro">Coverage Highlights</h3>
                <ul className="event-highlights-list">
                  <li>Live broadcasting with real-time commentary and analysis</li>
                  <li>Professional photography capturing key moments</li>
                  <li>Post-event interviews with athletes and coaches</li>
                  <li>Comprehensive highlight packages for digital platforms</li>
                </ul>
              </div>

              <div className="event-modal-cta-pro">
                <a href="#contact" className="event-modal-cta-button-pro">
                  Request Similar Coverage <i className="fas fa-arrow-right"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default EventsSection;
