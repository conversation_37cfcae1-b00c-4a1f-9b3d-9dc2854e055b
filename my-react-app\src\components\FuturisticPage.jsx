import React, { useState, useEffect } from 'react';
import FuturisticFooter from './FuturisticFooter';
import '../styles/FuturisticTheme.css';

// Particle effect component
const ParticleBackground = () => {
  useEffect(() => {
    // Create and animate particles
    const createParticles = () => {
      const particleContainer = document.getElementById('particle-container');
      if (!particleContainer) return;

      // Clear existing particles
      particleContainer.innerHTML = '';

      // Create new particles
      const particleCount = window.innerWidth < 768 ? 30 : 60;

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'futuristic-particle';

        // Random position
        const posX = Math.random() * 100;
        const posY = Math.random() * 100;

        // Random size
        const size = Math.random() * 3 + 1;

        // Random opacity
        const opacity = Math.random() * 0.5 + 0.1;

        // Random color (blue or red)
        const color = Math.random() > 0.7
          ? 'var(--futuristic-highlight)'
          : 'var(--futuristic-accent)';

        // Random animation duration
        const duration = Math.random() * 20 + 10;

        // Apply styles
        particle.style.cssText = `
          left: ${posX}%;
          top: ${posY}%;
          width: ${size}px;
          height: ${size}px;
          opacity: ${opacity};
          background-color: ${color};
          animation-duration: ${duration}s;
        `;

        particleContainer.appendChild(particle);
      }
    };

    // Initial creation
    createParticles();

    // Recreate on resize
    window.addEventListener('resize', createParticles);

    return () => {
      window.removeEventListener('resize', createParticles);
    };
  }, []);

  return (
    <div
      id="particle-container"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 0,
        overflow: 'hidden'
      }}
    />
  );
};

// Loading animation component
const FuturisticLoader = () => {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      background: 'var(--futuristic-dark)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 9999,
      transition: 'opacity 0.5s ease, visibility 0.5s ease'
    }}>
      <div style={{
        width: '80px',
        height: '80px',
        border: '3px solid transparent',
        borderTop: '3px solid var(--futuristic-accent)',
        borderRight: '3px solid var(--futuristic-accent)',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
        marginBottom: '1.5rem',
        boxShadow: 'var(--futuristic-glow)'
      }}></div>
      <p style={{
        color: 'var(--futuristic-text)',
        fontSize: '1.2rem',
        fontWeight: '500',
        letterSpacing: '2px',
        textTransform: 'uppercase'
      }}>Loading</p>
    </div>
  );
};

// Futuristic page wrapper component
const FuturisticPage = ({ children, pageTitle }) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Change body background to match futuristic theme
    document.body.classList.add('futuristic-bg');

    // Add page title if provided
    if (pageTitle) {
      document.title = `${pageTitle} | Siddartha Manubothu`;
    }

    // Simulate loading time
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    // Clean up when component unmounts
    return () => {
      clearTimeout(timer);
      document.body.classList.remove('futuristic-bg');
    };
  }, [pageTitle]);

  return (
    <div className="page-container" style={{ background: 'var(--futuristic-dark)' }}>
      {loading && <FuturisticLoader />}

      <ParticleBackground />

      {/* Page content */}
      <main className="page-content" style={{
        position: 'relative',
        zIndex: 1,
        opacity: loading ? 0 : 1,
        transition: 'opacity 0.5s ease'
      }}>
        {children}
      </main>

      <FuturisticFooter />
    </div>
  );
};

export default FuturisticPage;
