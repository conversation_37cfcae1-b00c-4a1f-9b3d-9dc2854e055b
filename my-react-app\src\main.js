// Ultra-fast vanilla JavaScript for lightning speed
class FastPortfolio {
  constructor() {
    this.images = [
      '/images/sports/750_0350-Enhanced-NR.jpg',
      '/images/sports/750_0441-Enhanced-NR.jpg',
      '/images/sports/DSC00061-Enhanced-NR.jpg',
      '/images/sports/DSC00113-Enhanced-NR.jpg',
      '/images/sports/DSC00156-Enhanced-NR.jpg',
      '/images/sports/DSC00231-Enhanced-NR.jpg',
      '/images/sports/DSC00732-Enhanced-NR.jpg',
      '/images/sports/DSC04917-Enhanced-NR.jpg',
      '/images/sports/DSC05591-Enhanced-NR.jpg',
      '/images/sports/DSC05711-Enhanced-NR.jpg',
      '/images/sports/DSC06401-Enhanced-NR.jpg',
      '/images/sports/DSC06411-Enhanced-NR.jpg',
      '/images/sports/DSC07025.jpg',
      '/images/sports/DSC07308-Enhanced-NR.jpg',
      '/images/sports/DSC07394-Enhanced-NR.jpg',
      '/images/sports/DSC08306-Enhanced-NR.jpg',
      '/images/sports/DSC08558-Enhanced-NR.jpg',
      '/images/sports/DSC08928.jpg',
      '/images/sports/DSC09229-Enhanced-NR.jpg',
      '/images/sports/DSC09584-Enhanced-NR.jpg',
      '/images/sports/DSC09840-Enhanced-NR.jpg',
      '/images/sports/DSC_1913.jpg',
      '/images/sports/DSC_4909-Enhanced-NR.jpg',
      '/images/sports/DSC_5152-Enhanced-NR.jpg',
      '/images/sports/DSC_5338-Enhanced-NR.jpg',
      '/images/sports/DSC_5376.jpg',
      '/images/sports/DSC_5608-Enhanced-NR.jpg',
      '/images/sports/DSC_5713.jpg'
    ];

    this.portfolioData = [
      {
        title: 'Sports Photography',
        description: 'Capturing decisive moments in sports through professional photography',
        image: '/images/sports/750_0441-Enhanced-NR.jpg',
        stats: '1000+ Photos'
      },
      {
        title: 'Sports Videography',
        description: 'Cinematic sports storytelling through dynamic video production',
        image: '/images/sports/DSC00061-Enhanced-NR.jpg',
        stats: '25+ Productions'
      },
      {
        title: 'Graphic Design',
        description: 'Creative visual solutions for sports brands and events',
        image: '/images/sports/DSC00113-Enhanced-NR.jpg',
        stats: '100+ Designs'
      },
      {
        title: 'Event Coverage',
        description: 'Comprehensive sports event coverage and media production',
        image: '/images/sports/DSC00156-Enhanced-NR.jpg',
        stats: '30+ Major Events'
      }
    ];

    this.init();
  }

  async init() {
    // Preload critical images
    await this.preloadImages();
    
    // Load content
    this.loadPortfolio();
    this.loadGallery();
    this.setupNavigation();
    
    // Hide loading screen
    setTimeout(() => {
      document.getElementById('loading').classList.add('hidden');
    }, 500);
  }

  async preloadImages() {
    const criticalImages = this.images.slice(0, 8); // Load first 8 images immediately
    const promises = criticalImages.map(src => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = resolve;
        img.onerror = resolve;
        img.src = src;
      });
    });
    
    await Promise.all(promises);
  }

  loadPortfolio() {
    const grid = document.getElementById('portfolioGrid');
    grid.innerHTML = this.portfolioData.map(item => `
      <div class="portfolio-card">
        <img src="${item.image}" alt="${item.title}" class="card-image" loading="lazy">
        <div class="card-content">
          <h3 class="card-title">${item.title}</h3>
          <p class="card-description">${item.description}</p>
          <div class="card-stats">${item.stats}</div>
        </div>
      </div>
    `).join('');
  }

  loadGallery() {
    const gallery = document.getElementById('galleryGrid');
    
    // Load images in batches for better performance
    const batchSize = 6;
    let currentBatch = 0;
    
    const loadBatch = () => {
      const start = currentBatch * batchSize;
      const end = Math.min(start + batchSize, this.images.length);
      
      for (let i = start; i < end; i++) {
        const img = this.images[i];
        const item = document.createElement('div');
        item.className = 'gallery-item';
        item.innerHTML = `<img src="${img}" alt="Sports Photo ${i + 1}" class="gallery-img" loading="lazy">`;
        gallery.appendChild(item);
      }
      
      currentBatch++;
      
      // Load next batch after a short delay
      if (end < this.images.length) {
        setTimeout(loadBatch, 100);
      }
    };
    
    loadBatch();
  }

  setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        // Update active state
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        
        // Smooth scroll to section
        const target = link.getAttribute('href');
        const section = document.querySelector(target);
        if (section) {
          section.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    // Scroll spy
    window.addEventListener('scroll', () => {
      const sections = ['home', 'portfolio', 'gallery', 'contact'];
      const scrollPos = window.scrollY + 100;
      
      sections.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
          const top = section.offsetTop;
          const bottom = top + section.offsetHeight;
          
          if (scrollPos >= top && scrollPos <= bottom) {
            navLinks.forEach(l => l.classList.remove('active'));
            const activeLink = document.querySelector(`[href="#${sectionId}"]`);
            if (activeLink) activeLink.classList.add('active');
          }
        }
      });
    });
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => new FastPortfolio());
} else {
  new FastPortfolio();
}

// Service Worker for caching (optional but recommended for speed)
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js').catch(() => {
      // Silently fail if service worker registration fails
    });
  });
}
