<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON>ubothu - Sports Media Professional</title>

  <!-- Preload critical images for instant loading -->
  <link rel="preload" href="/images/sports/750_0350-Enhanced-NR.jpg" as="image">
  <link rel="preload" href="/images/sports/750_0441-Enhanced-NR.jpg" as="image">
  <link rel="preload" href="/images/sports/DSC00061-Enhanced-NR.jpg" as="image">
  <link rel="preload" href="/images/profile/siddu.jpg" as="image">

  <!-- Critical CSS inline for instant loading -->
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }

    :root {
      --primary: #0e3b7d;
      --secondary: #e63946;
      --accent: #457b9d;
      --dark: #1d3557;
      --light: #f1faee;
      --white: #ffffff;
      --shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      line-height: 1.6;
      color: #333;
      overflow-x: hidden;
      background: #f8f9fa;
    }

    .loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      color: white;
      font-size: 1.2rem;
      transition: opacity 0.5s ease;
    }

    .loading.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255,255,255,0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .navbar {
      position: fixed;
      top: 0;
      width: 100%;
      background: rgba(255,255,255,0.95);
      backdrop-filter: blur(20px);
      z-index: 1000;
      padding: 1rem 0;
      box-shadow: var(--shadow);
    }

    .nav-container {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 2rem;
    }

    .logo {
      font-size: 1.8rem;
      font-weight: 800;
      color: var(--primary);
      text-decoration: none;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-link {
      color: var(--primary);
      text-decoration: none;
      font-weight: 600;
      transition: color 0.3s ease;
      cursor: pointer;
    }

    .nav-link:hover, .nav-link.active {
      color: var(--secondary);
    }

    .hero {
      min-height: 100vh;
      display: flex;
      align-items: center;
      padding: 2rem;
      background: linear-gradient(135deg, var(--primary) 0%, var(--dark) 100%);
      color: white;
      position: relative;
    }

    .hero-content {
      max-width: 1200px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
    }

    .hero-text h1 {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 800;
      margin-bottom: 1rem;
    }

    .hero-text p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .profile-img {
      width: 300px;
      height: 300px;
      border-radius: 50%;
      object-fit: cover;
      border: 4px solid rgba(255,255,255,0.2);
      box-shadow: 0 20px 40px rgba(0,0,0,0.3);
      margin: 0 auto;
      display: block;
    }

    .cta-button {
      display: inline-block;
      background: var(--secondary);
      color: white;
      padding: 1rem 2rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: var(--shadow);
    }

    .cta-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(230, 57, 70, 0.4);
    }

    .section {
      padding: 5rem 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .section h2 {
      font-size: 2.5rem;
      font-weight: 800;
      color: var(--primary);
      margin-bottom: 3rem;
      text-align: center;
    }

    .portfolio-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .portfolio-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: var(--shadow);
      transition: transform 0.3s ease;
      cursor: pointer;
    }

    .portfolio-card:hover {
      transform: translateY(-5px);
    }

    .card-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .card-content {
      padding: 1.5rem;
    }

    .card-title {
      font-size: 1.3rem;
      font-weight: 700;
      color: var(--primary);
      margin-bottom: 0.5rem;
    }

    .card-description {
      color: #666;
      margin-bottom: 1rem;
    }

    .card-stats {
      color: var(--secondary);
      font-weight: 600;
      font-size: 0.9rem;
    }

    .gallery {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
      margin-top: 2rem;
    }

    .gallery-item {
      aspect-ratio: 4/3;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: var(--shadow);
      transition: transform 0.3s ease;
    }

    .gallery-item:hover {
      transform: scale(1.05);
    }

    .gallery-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .contact {
      background: var(--primary);
      color: white;
      text-align: center;
    }

    .contact h2 {
      color: white;
    }

    .contact-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .contact-item {
      padding: 1.5rem;
      background: rgba(255,255,255,0.1);
      border-radius: 8px;
    }

    .contact-item i {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: var(--secondary);
    }

    @media (max-width: 768px) {
      .nav-links { display: none; }
      .hero-content { grid-template-columns: 1fr; text-align: center; }
      .profile-img { width: 250px; height: 250px; }
      .portfolio-grid { grid-template-columns: 1fr; }
      .gallery { grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); }
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <span>Loading Portfolio...</span>
  </div>

  <!-- Navigation -->
  <nav class="navbar">
    <div class="nav-container">
      <a href="#" class="logo">SM</a>
      <ul class="nav-links">
        <li><a href="#home" class="nav-link active">Home</a></li>
        <li><a href="#photography" class="nav-link">Photography</a></li>
        <li><a href="#videography" class="nav-link">Videography</a></li>
        <li><a href="#graphics" class="nav-link">Graphics</a></li>
        <li><a href="#events" class="nav-link">Events</a></li>
        <li><a href="#contact" class="nav-link">Contact</a></li>
      </ul>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero" id="home">
    <div class="hero-content">
      <div class="hero-text">
        <h1>Siddartha Manubothu</h1>
        <p>Sports Media Professional specializing in Broadcasting, Photography, Videography, and Graphic Design</p>
        <a href="#portfolio" class="cta-button">View My Work</a>
      </div>
      <div class="hero-image">
        <img src="/images/profile/siddu.jpg" alt="Siddartha Manubothu" class="profile-img" loading="eager">
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section class="section" id="portfolio">
    <h2>My Portfolio</h2>
    <div class="portfolio-grid" id="portfolioGrid">
      <!-- Portfolio cards will be loaded here -->
    </div>
  </section>

  <!-- Gallery Section -->
  <section class="section" id="gallery">
    <h2>Sports Gallery</h2>
    <div class="gallery" id="galleryGrid">
      <!-- Gallery images will be loaded here -->
    </div>
  </section>

  <!-- Contact Section -->
  <section class="section contact" id="contact">
    <h2>Get In Touch</h2>
    <div class="contact-info">
      <div class="contact-item">
        <i class="fas fa-envelope"></i>
        <h3>Email</h3>
        <p><EMAIL></p>
      </div>
      <div class="contact-item">
        <i class="fas fa-phone"></i>
        <h3>Phone</h3>
        <p>+91 98765 43210</p>
      </div>
      <div class="contact-item">
        <i class="fas fa-map-marker-alt"></i>
        <h3>Location</h3>
        <p>India</p>
      </div>
    </div>
  </section>

  <script src="/src/main.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</body>
</html>
