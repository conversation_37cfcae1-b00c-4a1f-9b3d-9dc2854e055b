/* Photography Section Styles */
.photography-section {
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.photography-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(14, 59, 125, 0.05), transparent 70%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.05), transparent 70%);
  z-index: -1;
}

.photography-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.photography-intro h2.animated-title {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s forwards;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  letter-spacing: -0.5px;
}

.title-accent {
  width: 80px;
  height: 6px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin: 0 auto 2rem;
  border-radius: 3px;
  transform: scaleX(0);
  transform-origin: center;
  animation: scaleIn 1.2s forwards 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.photography-intro p.animated-text {
  font-size: 1.3rem;
  line-height: 1.8;
  color: #444;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s forwards 0.3s;
  max-width: 800px;
  margin: 0 auto 2.5rem;
  font-weight: 400;
}

.intro-badges {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 2.5rem;
  flex-wrap: wrap;
}

.intro-badge {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: white;
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s forwards;
  transition: all 0.3s ease;
}

.intro-badge:nth-child(1) {
  animation-delay: 0.6s;
}

.intro-badge:nth-child(2) {
  animation-delay: 0.8s;
}

.intro-badge:nth-child(3) {
  animation-delay: 1s;
}

.intro-badge:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
}

.intro-badge i {
  font-size: 1.2rem;
  color: var(--primary-color);
}

.intro-badge span {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

@keyframes scaleIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* Featured Photo Showcase */
.featured-photo-showcase {
  margin: 4rem auto;
  max-width: 1400px;
  padding: 0 2rem;
}

.featured-photo-container {
  position: relative;
  width: 100%;
  height: 600px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
  transform: translateY(30px);
  opacity: 0;
  animation: fadeInUp 1s forwards 0.5s;
}

.featured-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);
  transform: scale(1.05);
}

.featured-photo-container:hover .featured-photo {
  transform: scale(1);
}

.featured-photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 100%);
  display: flex;
  align-items: flex-end;
  padding: 3rem;
  transition: all 0.5s ease;
}

.featured-photo-container:hover .featured-photo-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.5) 50%, rgba(0, 0, 0, 0.2) 100%);
}

.featured-photo-content {
  max-width: 600px;
  color: white;
}

.featured-photo-label {
  display: inline-block;
  padding: 0.5rem 1.2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp 0.8s forwards 0.8s;
}

.featured-photo-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  text-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp 0.8s forwards 1s;
}

.featured-photo-description {
  font-size: 1.2rem;
  line-height: 1.7;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
  opacity: 0.9;
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp 0.8s forwards 1.2s;
}

@media (max-width: 992px) {
  .featured-photo-container {
    height: 500px;
  }

  .featured-photo-overlay {
    padding: 2rem;
  }

  .featured-photo-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .featured-photo-container {
    height: 400px;
  }

  .featured-photo-title {
    font-size: 1.8rem;
  }

  .featured-photo-description {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .featured-photo-container {
    height: 350px;
  }

  .featured-photo-overlay {
    padding: 1.5rem;
  }

  .featured-photo-title {
    font-size: 1.5rem;
  }

  .featured-photo-description {
    font-size: 1rem;
    line-height: 1.5;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes lineGrow {
  from {
    width: 0;
  }
  to {
    width: 80px;
  }
}

/* Category Filter */
.photo-filter {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
  position: sticky;
  top: 80px;
  z-index: 10;
  padding: 1rem 0;
  background-color: rgba(248, 249, 250, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.photo-filter.scrolled {
  padding: 0.8rem 0;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.filter-button {
  padding: 0.8rem 1.5rem;
  background-color: white;
  color: #333;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.filter-name {
  position: relative;
  z-index: 2;
}

.filter-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 8px;
  background-color: #f0f0f0;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 700;
  color: #555;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  z-index: 1;
}

.filter-button:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.filter-button:hover .filter-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.filter-button:hover::before {
  opacity: 1;
}

.filter-button.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.25);
}

.filter-button.active .filter-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Masonry Grid */
.photo-masonry {
  column-count: 3;
  column-gap: 2rem;
  margin: 4rem auto;
  width: 100%;
  max-width: 1400px;
  padding: 0 2rem;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s forwards 0.2s;
  will-change: opacity, transform;
}

@media (min-width: 1600px) {
  .photo-masonry {
    column-count: 4;
  }
}

@media (max-width: 1400px) {
  .photo-masonry {
    column-count: 3;
  }
}

@media (max-width: 992px) {
  .photo-masonry {
    column-count: 2;
    column-gap: 1.5rem;
  }
}

@media (max-width: 576px) {
  .photo-masonry {
    column-count: 1;
    padding: 0 1.5rem;
  }
}

.photo-item {
  break-inside: avoid;
  margin-bottom: 2rem;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  cursor: pointer;
  transform: translateY(50px);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  background-color: white;
  will-change: transform, opacity, box-shadow;
  transform-origin: center bottom;
}

.photo-item.animated {
  transform: translateY(0);
  opacity: 1;
}

.photo-item-inner {
  position: relative;
  overflow: hidden;
  height: 100%;
}

.photo-item:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  z-index: 5;
}

.photo-image {
  width: 100%;
  height: auto;
  display: block;
  transition: all 0.7s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: transform;
  object-fit: cover;
}

.photo-item:hover .photo-image {
  transform: scale(1.1);
}

.photo-hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(14, 59, 125, 0.3), rgba(230, 57, 70, 0.3));
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 1;
}

.photo-item:hover .photo-hover-effect {
  opacity: 1;
}

/* Add a subtle border to each photo */
.photo-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-sizing: border-box;
  pointer-events: none;
  z-index: 2;
}

/* Loading Animation */
.photo-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  margin: 2rem 0;
}

.loading-progress {
  width: 80%;
  max-width: 500px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

.progress-bar {
  height: 10px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 10px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-color);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.photo-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.6) 60%, rgba(0, 0, 0, 0.1));
  color: white;
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: opacity, transform;
  pointer-events: none;
  z-index: 3;
}

.photo-item:hover .photo-info {
  opacity: 1;
  transform: translateY(0);
}

.photo-category {
  display: inline-block;
  padding: 0.5rem 1.2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 30px;
  font-size: 0.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  transform: translateY(15px);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1) 0.1s;
  letter-spacing: 0.5px;
}

.photo-item:hover .photo-category {
  transform: translateY(0);
  opacity: 1;
}

.photo-title {
  font-size: 1.4rem;
  margin: 0 0 0.8rem 0;
  font-weight: 800;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.6);
  transform: translateY(15px);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1) 0.2s;
  letter-spacing: -0.5px;
}

.photo-item:hover .photo-title {
  transform: translateY(0);
  opacity: 1;
}

.photo-description {
  font-size: 1rem;
  margin: 0;
  opacity: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
  transform: translateY(15px);
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1) 0.3s;
  line-height: 1.6;
  max-width: 90%;
}

.photo-item:hover .photo-description {
  transform: translateY(0);
  opacity: 0.95;
}

/* Enhanced Professional Lightbox */
.photo-lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  backdrop-filter: blur(15px);
  will-change: opacity;
}

.photo-lightbox.visible {
  opacity: 1;
}

.lightbox-container {
  position: relative;
  width: 92%;
  max-width: 1400px;
  max-height: 92vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 40px 80px rgba(0, 0, 0, 0.5);
  transform: scale(0.9) translateY(30px);
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: transform, opacity;
}

.photo-lightbox.visible .lightbox-container {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.lightbox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  position: relative;
  z-index: 10;
}

.lightbox-title {
  font-size: 1.8rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  letter-spacing: -0.5px;
}

.lightbox-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  font-size: 1.2rem;
  color: white;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  backdrop-filter: blur(5px);
}

.lightbox-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.lightbox-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.lightbox-action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: #f8f9fa;
  border: none;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.lightbox-action-button:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.2);
}

.lightbox-action-button i {
  font-size: 1rem;
}

.lightbox-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  overflow: hidden;
  position: relative;
  min-height: 400px;
}

.lightbox-image {
  max-width: 100%;
  max-height: 75vh;
  object-fit: contain;
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
  opacity: 0;
  transform: scale(0.95);
  will-change: transform, opacity;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.photo-lightbox.visible .lightbox-image {
  opacity: 1;
  transform: scale(1);
  transition-delay: 0.2s;
}

.lightbox-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  z-index: 5;
}

.lightbox-image-container:hover .lightbox-nav {
  opacity: 1;
}

.lightbox-nav-button {
  background: linear-gradient(135deg, rgba(14, 59, 125, 0.7), rgba(28, 76, 156, 0.7));
  color: white;
  border: none;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.5rem;
  backdrop-filter: blur(8px);
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  will-change: transform, background-color;
  opacity: 0.8;
}

.lightbox-nav-button:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  transform: scale(1.15);
  opacity: 1;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.lightbox-counter {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, rgba(14, 59, 125, 0.8), rgba(28, 76, 156, 0.8));
  color: white;
  padding: 8px 20px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 700;
  backdrop-filter: blur(8px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  opacity: 0;
  transform: translateX(-50%) translateY(20px);
  will-change: opacity, transform;
}

.photo-lightbox.visible .lightbox-counter {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  transition-delay: 0.3s;
}

.lightbox-details {
  padding: 2rem;
  background-color: white;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: transform, opacity;
}

.photo-lightbox.visible .lightbox-details {
  transform: translateY(0);
  opacity: 1;
  transition-delay: 0.3s;
}

.lightbox-category {
  display: inline-block;
  padding: 0.5rem 1.2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

.lightbox-description {
  font-size: 1.15rem;
  line-height: 1.8;
  color: #444;
  margin-bottom: 2rem;
  font-weight: 400;
  max-width: 900px;
}

.lightbox-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 2.5rem;
  padding-top: 1.8rem;
  border-top: 1px solid #eee;
  background: linear-gradient(to right, rgba(248, 249, 250, 0.5), rgba(255, 255, 255, 0.8));
  border-radius: 8px;
  padding: 1.8rem;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  transform: translateY(15px);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.photo-lightbox.visible .metadata-item:nth-child(1) {
  transition-delay: 0.4s;
  transform: translateY(0);
  opacity: 1;
}

.photo-lightbox.visible .metadata-item:nth-child(2) {
  transition-delay: 0.5s;
  transform: translateY(0);
  opacity: 1;
}

.photo-lightbox.visible .metadata-item:nth-child(3) {
  transition-delay: 0.6s;
  transform: translateY(0);
  opacity: 1;
}

.photo-lightbox.visible .metadata-item:nth-child(4) {
  transition-delay: 0.7s;
  transform: translateY(0);
  opacity: 1;
}

.metadata-label {
  font-size: 0.9rem;
  color: #777;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.metadata-value {
  font-size: 1.1rem;
  color: #222;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Photo Counter */
.photo-counter {
  text-align: center;
  margin: 3rem 0;
  font-size: 1.2rem;
  color: #555;
}

.photo-counter span {
  font-weight: 700;
  color: var(--primary-color);
}

/* Enhanced Loading Animation */
.photo-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  gap: 2rem;
}

.loading-spinner {
  position: relative;
  width: 70px;
  height: 70px;
}

.loading-spinner::before,
.loading-spinner::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.loading-spinner::before {
  border: 4px solid rgba(14, 59, 125, 0.1);
}

.loading-spinner::after {
  border: 4px solid transparent;
  border-top-color: var(--primary-color);
  border-left-color: var(--secondary-color);
  animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
}

.loading-text {
  font-size: 1.2rem;
  font-weight: 600;
  color: #555;
  letter-spacing: 1px;
  opacity: 0.8;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  from {
    opacity: 0.5;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* Empty State */
.photo-empty {
  text-align: center;
  padding: 5rem 0;
  color: #888;
}

.photo-empty h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #555;
}

.photo-empty p {
  font-size: 1.1rem;
  max-width: 500px;
  margin: 0 auto;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .photography-intro h2 {
    font-size: 2.5rem;
  }

  .photography-intro p {
    font-size: 1.1rem;
  }

  .lightbox-container {
    width: 95%;
  }

  .lightbox-metadata {
    flex-wrap: wrap;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .photography-intro h2 {
    font-size: 2rem;
  }

  .photo-filter {
    overflow-x: auto;
    justify-content: flex-start;
    padding: 1rem;
  }

  .filter-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    flex-shrink: 0;
  }

  .lightbox-image {
    max-height: 60vh;
  }

  .lightbox-nav-button {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

@media (max-width: 576px) {
  .photography-intro h2 {
    font-size: 1.8rem;
  }

  .photography-intro p {
    font-size: 1rem;
  }

  .lightbox-title {
    font-size: 1.2rem;
  }

  .lightbox-description {
    font-size: 1rem;
  }

  .lightbox-metadata {
    flex-direction: column;
    gap: 0.8rem;
  }
}
