.header {
  position: relative;
  height: 100vh;
  min-height: 600px;
  max-height: 900px;
  color: var(--text-light);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url('/images/sports/sport1.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  transition: all 0.5s ease;
  perspective: 1000px;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(230, 57, 70, 0.3), transparent 60%),
              radial-gradient(circle at bottom left, rgba(14, 59, 125, 0.3), transparent 60%);
  z-index: 1;
  opacity: 0.8;
  mix-blend-mode: overlay;
  pointer-events: none;
  animation: gradientBG 15s ease infinite alternate;
}

.header-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.header-particles .particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  pointer-events: none;
  animation: float-particle 25s infinite ease-in-out;
  opacity: 0.5;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(14, 59, 125, 0.85) 0%, rgba(29, 53, 87, 0.9) 100%);
  z-index: 1;
  backdrop-filter: blur(3px);
  animation: fadeIn 1.2s ease-out forwards;
}

.header-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(230, 57, 70, 0.2) 0%,
    rgba(14, 59, 125, 0.2) 50%,
    rgba(6, 214, 160, 0.2) 100%);
  background-size: 200% 200%;
  animation: gradientBG 8s ease infinite;
  z-index: 1;
  opacity: 0.6;
  mix-blend-mode: overlay;
}

.header-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.header-profile-image {
  width: 350px;
  height: 350px;
  margin: 0 auto 2rem;
  opacity: 0;
  transform: translateY(30px) rotate(-2deg);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 2;
  perspective: 1000px;
}

.profile-image-container {
  width: 100%;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 0 5px rgba(255, 255, 255, 0.2);
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.profile-image-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(circle at center, rgba(230, 57, 70, 0.5), transparent 70%);
  filter: blur(20px);
  opacity: 0.5;
  z-index: -1;
  transform: translateZ(-10px);
  animation: pulse 3s ease-in-out infinite alternate;
}

.profile-accent {
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  animation: rotate 30s linear infinite;
  pointer-events: none;
  z-index: -1;
  transform: translateZ(-5px);
}

.profile-accent::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  animation: rotate 20s linear infinite reverse;
}

.header-profile-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.2) 0%, rgba(14, 59, 125, 0.2) 100%);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: 15px;
}

.header-profile-image.visible {
  opacity: 1;
  transform: translateY(0) rotate(0deg);
}

.header-profile-image:hover .profile-image-container {
  transform: translateY(-10px) scale(1.02) rotateY(5deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 5px rgba(255, 255, 255, 0.3);
}

.header-profile-image:hover::after {
  opacity: 1;
}

.header-profile-image:hover .profile-image-glow {
  opacity: 0.7;
  filter: blur(25px);
}

.header-profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: contrast(1.05) saturate(1.1);
}

.header-profile-image:hover img {
  transform: scale(1.08);
}

.header-text-container {
  max-width: 600px;
  margin: 0 auto;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
  position: relative;
  z-index: 2;
  perspective: 1000px;
}

.header-text-container.visible {
  opacity: 1;
  transform: translateY(0);
}

.main-title {
  font-size: 4.8rem;
  margin-bottom: 1.5rem;
  font-weight: 800;
  letter-spacing: -2px;
  line-height: 1.1;
  text-transform: uppercase;
  position: relative;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to right, var(--text-light) 0%, rgba(255, 255, 255, 0.9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
  transform-style: preserve-3d;
}

.main-title::before {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(to right, var(--secondary-color) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0.15;
  transform: translateX(-5px) translateY(5px) translateZ(-10px);
  filter: blur(6px);
  animation: textShadowPulse 3s ease-in-out infinite alternate;
}

.title-underline {
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color), var(--secondary-color));
  background-size: 200% 100%;
  animation: gradientBG 5s ease infinite;
  border-radius: 2px;
  transform: translateZ(-5px);
  opacity: 0.7;
  box-shadow: 0 2px 10px rgba(230, 57, 70, 0.3);
}

.title-highlight {
  color: var(--secondary-color);
  position: relative;
  display: inline-block;
  background: linear-gradient(to right, var(--secondary-color) 0%, var(--secondary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform-style: preserve-3d;
}

.title-highlight::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(to right, var(--secondary-color) 0%, var(--secondary-light) 100%);
  opacity: 0.4;
  border-radius: 4px;
  transform: scaleX(0.9) translateZ(-5px);
  transform-origin: left;
  transition: all 0.3s ease;
  animation: pulse 3s ease-in-out infinite alternate;
}

.title-highlight:hover::after {
  transform: scaleX(1.05) translateZ(-5px);
  opacity: 0.6;
  box-shadow: 0 5px 15px rgba(230, 57, 70, 0.4);
}

.subtitle {
  font-size: 2.6rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  font-weight: 600;
  letter-spacing: -0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
  transform-style: preserve-3d;
  animation: fadeIn 0.5s 0.5s backwards;
}

.subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%) translateZ(-5px);
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(230, 57, 70, 0.3);
  animation: pulse 3s ease-in-out infinite alternate;
}

.tagline {
  font-size: 1.5rem;
  color: rgba(241, 250, 238, 0.95);
  margin: 2rem 0;
  font-weight: 400;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  padding: 0 1rem;
  animation: fadeIn 0.5s 0.7s backwards;
  transform-style: preserve-3d;
}

.header-badges {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
  perspective: 1000px;
  animation: fadeIn 0.5s 0.9s backwards;
}

.header-badge {
  background-color: rgba(255, 255, 255, 0.12);
  color: var(--text-light);
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  font-size: 0.95rem;
  font-weight: 500;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), inset 0 1px 1px rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.header-badge i {
  font-size: 1.2rem;
  color: var(--secondary-color);
  transition: all 0.3s ease;
}

.header-badge::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.5s ease;
  pointer-events: none;
}

.header-badge::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.1) 0%, rgba(14, 59, 125, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.header-badge:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px) rotateX(10deg);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.header-badge:hover i {
  color: var(--secondary-light);
  transform: scale(1.2);
}

.header-badge:hover::before {
  opacity: 1;
  transform: scale(1);
}

.header-badge:hover::after {
  opacity: 1;
}

.header-badge:nth-child(1) {
  animation: fadeIn 0.5s 0.6s backwards;
}

.header-badge:nth-child(2) {
  animation: fadeIn 0.5s 0.8s backwards;
}

.header-badge:nth-child(3) {
  animation: fadeIn 0.5s 1s backwards;
}

.header-badge:nth-child(4) {
  animation: fadeIn 0.5s 1.2s backwards;
}

.header-cta {
  margin-top: 3rem;
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
  animation: fadeIn 0.5s 1.4s backwards;
  perspective: 1000px;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  border-radius: 50px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  transform: translateZ(0);
  backface-visibility: hidden;
  gap: 0.8rem;
  transform-style: preserve-3d;
}

.cta-button span {
  position: relative;
  z-index: 2;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.8s ease;
  z-index: 1;
}

.cta-button:hover::before {
  transform: translateX(100%);
}

.button-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: inherit;
  filter: blur(20px);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
  transform: translateZ(-10px);
}

.cta-button:hover .button-glow {
  opacity: 0.5;
}

.cta-button.primary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: var(--text-light);
  border: none;
}

.cta-button.primary:hover {
  background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
  color: var(--text-light);
  text-decoration: none;
  box-shadow: 0 15px 30px rgba(230, 57, 70, 0.4);
  transform: translateY(-8px) scale(1.03) rotateX(10deg);
}

.cta-button.primary:active {
  transform: translateY(-3px) scale(0.98);
  box-shadow: 0 8px 15px rgba(230, 57, 70, 0.3);
}

.cta-button.secondary {
  background-color: transparent;
  color: var(--text-light);
  border: 2px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), inset 0 1px 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.cta-button.secondary::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.cta-button.secondary:hover {
  border-color: var(--text-light);
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25);
  transform: translateY(-8px) scale(1.03) rotateX(10deg);
}

.cta-button.secondary:hover::after {
  opacity: 1;
}

.cta-button.secondary:active {
  transform: translateY(-3px) scale(0.98);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.cta-button i {
  font-size: 1.2rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.cta-button:hover i {
  transform: translateX(5px);
  color: rgba(255, 255, 255, 1);
}

.header-accent {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 15px;
  background: linear-gradient(90deg, var(--secondary-color), var(--primary-color), var(--secondary-color));
  background-size: 200% 100%;
  z-index: 3;
  animation: gradientBG 8s ease infinite;
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1);
}

.header-accent::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 0;
  width: 100%;
  height: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.1), transparent);
}

.header-bottom-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  line-height: 0;
  z-index: 2;
  overflow: hidden;
}

.header-bottom-wave svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 80px;
  transform: rotateY(180deg);
}

.header-bottom-wave path {
  animation: none;
}

.scroll-down-container {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  animation: fadeIn 1s 1.6s backwards;
}

.scroll-down-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  cursor: pointer;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), inset 0 1px 3px rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.scroll-ring {
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  pointer-events: none;
}

.scroll-down-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.scroll-down-button:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), inset 0 1px 3px rgba(255, 255, 255, 0.2);
  animation-play-state: paused;
}

.scroll-down-button:hover::before {
  opacity: 1;
}

.scroll-down-button:hover .scroll-ring {
  animation-play-state: paused;
  opacity: 0;
}

.scroll-down-button:active {
  transform: translateY(-3px) scale(0.98);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

.scroll-down-button i {
  transition: transform 0.3s ease;
  animation: bounce 2s infinite;
}

.scroll-down-button:hover i {
  transform: translateY(3px);
  animation-play-state: paused;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-6px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 992px) {
  .header-content {
    flex-direction: column;
    justify-content: center;
  }

  .header-profile-image {
    margin-bottom: 2rem;
  }

  .header-bottom-wave svg {
    height: 60px;
  }

  .scroll-down-container {
    bottom: 80px;
  }

  .main-title {
    font-size: 4rem;
  }

  .cta-button {
    padding: 1rem 2rem;
  }
}

@media (max-width: 768px) {
  .header {
    height: auto;
    min-height: 100vh;
    padding: 6rem 0;
  }

  .main-title {
    font-size: 3rem;
  }

  .subtitle {
    font-size: 1.8rem;
  }

  .tagline {
    font-size: 1.2rem;
  }

  .header-profile-image {
    width: 280px;
    height: 280px;
  }

  .header-bottom-wave svg {
    height: 40px;
  }

  .scroll-down-container {
    bottom: 60px;
  }

  .scroll-down-button {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .header-cta {
    flex-direction: column;
    gap: 1rem;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
  }

  .header-badge {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .header-badges {
    gap: 0.8rem;
  }

  /* Disable parallax on mobile for better performance */
  .header-profile-image, .main-title, .subtitle, .tagline, .header-badges, .header-cta {
    transform: none !important;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2.5rem;
    letter-spacing: -1px;
  }

  .subtitle {
    font-size: 1.5rem;
  }

  .header-profile-image {
    width: 220px;
    height: 220px;
  }

  .header-badges {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .header-bottom-wave {
    display: none;
  }

  .scroll-down-container {
    bottom: 40px;
  }

  .scroll-down-button {
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  .tagline {
    font-size: 1.1rem;
  }

  .cta-button {
    padding: 0.9rem 1.8rem;
    font-size: 1rem;
  }

  .header-badge i {
    font-size: 1rem;
  }

  .header-particles .particle {
    display: none;
  }
}
