.footer {
  background-color: var(--dark-color);
  color: var(--text-light);
  padding: 3rem 0 2rem;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
}

.back-to-top {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.back-to-top-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: var(--transition);
}

.back-to-top-button:hover {
  background-color: var(--primary-color);
  transform: translateY(-5px);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-main {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.footer-logo {
  flex: 0 0 300px;
  margin-bottom: 1.5rem;
}

.footer-logo h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.footer-logo p {
  color: rgba(241, 250, 238, 0.7);
  line-height: 1.6;
}

.footer-nav {
  flex: 0 0 200px;
  margin-bottom: 1.5rem;
}

.footer-nav h4 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 0.5rem;
}

.footer-nav h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--secondary-color);
}

.footer-nav ul {
  list-style: none;
  padding: 0;
}

.footer-nav li {
  margin-bottom: 0.5rem;
}

.footer-nav a {
  color: rgba(241, 250, 238, 0.7);
  text-decoration: none;
  transition: var(--transition);
}

.footer-nav a:hover {
  color: var(--secondary-color);
}

.footer-contact {
  flex: 0 0 300px;
  margin-bottom: 1.5rem;
}

.footer-contact h4 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 0.5rem;
}

.footer-contact h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--secondary-color);
}

.contact-info {
  margin-bottom: 1.5rem;
}

.contact-info p {
  color: rgba(241, 250, 238, 0.7);
  margin-bottom: 0.5rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--text-light);
  text-decoration: none;
  transition: var(--transition);
}

.social-link:hover {
  background-color: var(--secondary-color);
  color: var(--text-light);
  transform: translateY(-3px);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-bottom p {
  color: rgba(241, 250, 238, 0.5);
  font-size: 0.9rem;
}

.footer-credit {
  color: var(--secondary-color);
  font-weight: 600;
  font-size: 0.85rem;
  letter-spacing: 1px;
}

@media (max-width: 992px) {
  .footer-logo, .footer-nav, .footer-contact {
    flex: 0 0 calc(50% - 1rem);
  }
}

@media (max-width: 768px) {
  .footer-main {
    flex-direction: column;
  }

  .footer-logo, .footer-nav, .footer-contact {
    flex: 0 0 100%;
    text-align: center;
  }

  .footer-nav h4::after, .footer-contact h4::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .social-links {
    justify-content: center;
  }
}
