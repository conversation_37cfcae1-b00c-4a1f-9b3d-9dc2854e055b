:root {
  font-family: 'Montserrat', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Professional Sports Theme Colors */
  --primary-color: #0e3b7d; /* Deep Blue */
  --secondary-color: #e63946; /* Vibrant Red */
  --accent-color: #457b9d; /* Muted Blue */
  --light-color: #f1faee; /* Off White */
  --dark-color: #1d3557; /* Dark Blue */
  --text-color: #333333; /* Dark Gray for Text */
  --background-color: #ffffff; /* White Background */
  --gray-light: #f8f9fa; /* Light Gray for Sections */
  --gray-medium: #e9ecef; /* Medium Gray for Borders */
  --success-color: #2a9d8f; /* Teal Green */
  --warning-color: #e9c46a; /* Mustard Yellow */
  --error-color: #e76f51; /* Coral Red */

  color-scheme: light;
  color: var(--text-color);
  background-color: var(--background-color);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

a {
  font-weight: 600;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

a:hover {
  color: var(--secondary-color);
}

body {
  margin: 0;
  padding: 0;
  padding-top: 70px;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  overflow-x: hidden;
}

/* Performance optimizations */
img {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

#root {
  width: 100%;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: none;
  padding: 0.7em 1.5em;
  font-size: 1em;
  font-weight: 600;
  font-family: inherit;
  background-color: var(--primary-color);
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

button:hover {
  background-color: var(--dark-color);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

button:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

button:focus,
button:focus-visible {
  outline: 3px solid rgba(14, 59, 125, 0.3);
  outline-offset: 2px;
}

/* Global Utility Classes */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 5rem 2rem;
}

.text-center {
  text-align: center;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
