import React, { useState, useEffect, useRef } from 'react';
import './Sections.css';
import './PremiumEventsSection.css';

const PremiumEventsSection = () => {
  const [activeCategory, setActiveCategory] = useState('All');
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [animatedItems, setAnimatedItems] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const sectionRef = useRef(null);
  const sliderRef = useRef(null);

  // Premium event data with event images
  const eventItems = [
    {
      id: 1,
      title: 'National Basketball Championship Finals',
      date: 'June 15, 2024',
      location: 'National Stadium, Delhi',
      description: 'Comprehensive coverage of the national basketball championship finals, featuring the country\'s top teams competing for the prestigious title. This event includes pre-game analysis, live commentary, post-game interviews with players and coaches, and in-depth highlight packages.',
      imageUrl: '/images/events/event1.jpg',
      category: 'Championship',
      tags: ['Live Coverage', 'Basketball', 'National'],
      gallery: [
        '/images/events/event1.jpg',
        '/images/events/event2.jpg',
        '/images/events/event3.jpg',
        '/images/events/event4.jpg'
      ],
      highlights: [
        'Live play-by-play commentary throughout the championship game',
        'Exclusive interviews with MVP players and coaching staff',
        'Comprehensive statistical analysis and game breakdowns',
        'Behind-the-scenes footage of team preparations and celebrations'
      ],
      stats: {
        attendees: '15,000+',
        broadcastReach: '2.5M',
        mediaTeam: '25 professionals',
        duration: '3 days'
      }
    },
    {
      id: 2,
      title: 'India vs Australia Cricket Series',
      date: 'July 22, 2024',
      location: 'Cricket Stadium, Mumbai',
      description: 'Full media coverage of the international cricket series between India and Australia, including match commentary, player interviews, and expert analysis. This high-profile event attracts global attention and features comprehensive digital and broadcast content production.',
      imageUrl: '/images/events/event2.jpg',
      category: 'International',
      tags: ['Cricket', 'Series Coverage', 'International'],
      gallery: [
        '/images/events/event2.jpg',
        '/images/events/event3.jpg',
        '/images/events/event4.jpg',
        '/images/events/event5.jpg'
      ],
      highlights: [
        'Ball-by-ball commentary for all five test matches',
        'Daily recap shows with expert panel discussions',
        'Player profile features and career retrospectives',
        'Technical analysis segments with former international players'
      ],
      stats: {
        attendees: '80,000+',
        broadcastReach: '150M',
        mediaTeam: '120 professionals',
        duration: '45 days'
      }
    },
    {
      id: 3,
      title: 'All-India College Football Tournament',
      date: 'August 10, 2024',
      location: 'University Stadium, Bangalore',
      description: 'Coverage of the annual inter-college football tournament featuring the top university teams from across the country. This event showcases emerging talent and includes feature stories on promising young athletes, team strategies, and the competitive collegiate sports landscape.',
      imageUrl: '/images/events/event3.jpg',
      category: 'Tournament',
      tags: ['Football', 'College Sports', 'Tournament'],
      gallery: [
        '/images/events/event3.jpg',
        '/images/events/event1.jpg',
        '/images/events/event4.jpg',
        '/images/events/event5.jpg'
      ],
      highlights: [
        'Coverage of all 32 tournament matches across three weeks',
        'Special segments on rising stars and NFL prospects',
        'Tactical analysis of innovative coaching strategies',
        'Documentary-style features on team rivalries and traditions'
      ],
      stats: {
        attendees: '25,000+',
        broadcastReach: '5M',
        mediaTeam: '40 professionals',
        duration: '21 days'
      }
    },
    {
      id: 4,
      title: 'Olympic Trials Media Day',
      date: 'September 5, 2024',
      location: 'Olympic Training Center, Delhi',
      description: 'Media coverage of the Olympic trials press conference, featuring interviews with athletes, coaches, and Olympic committee members. This event provides insights into the selection process, athlete preparations, and expectations for the upcoming Olympic Games.',
      imageUrl: '/images/events/event4.jpg',
      category: 'Press Conference',
      tags: ['Olympics', 'Press Coverage', 'Interviews'],
      gallery: [
        '/images/events/event4.jpg',
        '/images/events/event1.jpg',
        '/images/events/event2.jpg',
        '/images/events/event5.jpg'
      ],
      highlights: [
        'Exclusive one-on-one interviews with Olympic hopefuls',
        'In-depth coverage of selection criteria and qualification standards',
        'Feature stories on athletes\' training regimens and personal journeys',
        'Expert analysis of India\'s medal prospects across various sports'
      ],
      stats: {
        attendees: '500+',
        broadcastReach: '12M',
        mediaTeam: '35 professionals',
        duration: '2 days'
      }
    },
    {
      id: 5,
      title: 'South Asian Athletics Championship',
      date: 'October 18, 2024',
      location: 'Sports Complex, Chennai',
      description: 'Complete coverage of the regional athletics championship, featuring track and field events, swimming competitions, and team sports. This multi-day event showcases athletic talent from across the region and serves as a qualifier for national championships.',
      imageUrl: '/images/events/event5.jpg',
      category: 'Championship',
      tags: ['Athletics', 'Regional', 'Multi-sport'],
      gallery: [
        '/images/events/event5.jpg',
        '/images/events/event1.jpg',
        '/images/events/event2.jpg',
        '/images/events/event3.jpg'
      ],
      highlights: [
        'Live coverage of all final events across 12 sports disciplines',
        'Daily highlight packages and medal ceremony coverage',
        'Interviews with regional sports administrators and coaches',
        'Special features on community impact and sports development initiatives'
      ],
      stats: {
        attendees: '30,000+',
        broadcastReach: '25M',
        mediaTeam: '60 professionals',
        duration: '7 days'
      }
    },
    {
      id: 6,
      title: 'National Sports Awards Gala',
      date: 'November 30, 2024',
      location: 'Grand Hotel, Delhi',
      description: 'Coverage of the annual sports awards ceremony honoring outstanding athletes, coaches, and teams from the past year. This prestigious event includes red carpet interviews, acceptance speeches, and special feature segments on the year\'s most memorable sporting moments.',
      imageUrl: '/images/events/event1.jpg',
      category: 'Ceremony',
      tags: ['Awards', 'Gala', 'Recognition'],
      gallery: [
        '/images/events/event1.jpg',
        '/images/events/event2.jpg',
        '/images/events/event3.jpg',
        '/images/events/event4.jpg'
      ],
      highlights: [
        'Red carpet interviews with all major award nominees and presenters',
        'Live broadcast of the entire three-hour ceremony',
        'Behind-the-scenes access to preparation and rehearsals',
        'Retrospective segments celebrating the year\'s sporting achievements'
      ],
      stats: {
        attendees: '1,200+',
        broadcastReach: '35M',
        mediaTeam: '45 professionals',
        duration: '1 day'
      }
    }
  ];

  // Featured events for the slider
  const featuredEvents = eventItems.slice(0, 3);

  // Get unique categories
  const categories = ['All', ...new Set(eventItems.map(event => event.category))];

  // Filter events based on active category
  const filteredEvents = activeCategory === 'All'
    ? eventItems
    : eventItems.filter(event => event.category === activeCategory);

  // Open event details modal
  const openEventDetails = (event) => {
    setSelectedEvent(event);
    setIsModalOpen(true);
    document.body.style.overflow = 'hidden';
  };

  // Close event details modal
  const closeEventDetails = () => {
    setIsModalOpen(false);
    document.body.style.overflow = 'auto';
  };

  // Handle category change
  const handleCategoryChange = (category) => {
    setActiveCategory(category);
    setAnimatedItems([]);
    
    // Reset animation state to trigger new animations
    setTimeout(() => {
      const newAnimatedItems = [];
      filteredEvents.forEach((_, index) => {
        setTimeout(() => {
          newAnimatedItems.push(index);
          setAnimatedItems([...newAnimatedItems]);
        }, 100 * index);
      });
    }, 50);
  };

  // Slider navigation
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === featuredEvents.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? featuredEvents.length - 1 : prev - 1));
  };

  // Auto-advance slider
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 5000);

    return () => clearInterval(interval);
  }, [currentSlide]);

  // Initialize animations when component mounts
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          // Start animations for initial events
          const newAnimatedItems = [];
          filteredEvents.forEach((_, index) => {
            setTimeout(() => {
              newAnimatedItems.push(index);
              setAnimatedItems([...newAnimatedItems]);
            }, 150 * index);
          });
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  // Update animations when filtered events change
  useEffect(() => {
    setAnimatedItems([]);
    
    // Reset animation state to trigger new animations
    setTimeout(() => {
      const newAnimatedItems = [];
      filteredEvents.forEach((_, index) => {
        setTimeout(() => {
          newAnimatedItems.push(index);
          setAnimatedItems([...newAnimatedItems]);
        }, 100 * index);
      });
    }, 50);
  }, [filteredEvents]);

  return (
    <section className="section premium-events-section" id="events" ref={sectionRef}>
      {/* Hero Slider */}
      <div className="premium-hero-slider" ref={sliderRef}>
        <div className="slider-container" style={{ transform: `translateX(-${currentSlide * 100}%)` }}>
          {featuredEvents.map((event, index) => (
            <div key={index} className="slider-slide">
              <div className="slide-image" style={{ backgroundImage: `url(${event.imageUrl})` }}>
                <div className="slide-overlay">
                  <div className="slide-content">
                    <div className="slide-category">{event.category}</div>
                    <h2 className="slide-title">{event.title}</h2>
                    <div className="slide-meta">
                      <span className="slide-date"><i className="fas fa-calendar-alt"></i> {event.date}</span>
                      <span className="slide-location"><i className="fas fa-map-marker-alt"></i> {event.location}</span>
                    </div>
                    <p className="slide-description">{event.description.substring(0, 120)}...</p>
                    <button 
                      className="slide-button"
                      onClick={() => openEventDetails(event)}
                    >
                      View Details <i className="fas fa-arrow-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <button className="slider-arrow slider-prev" onClick={prevSlide}>
          <i className="fas fa-chevron-left"></i>
        </button>
        <button className="slider-arrow slider-next" onClick={nextSlide}>
          <i className="fas fa-chevron-right"></i>
        </button>
        
        <div className="slider-dots">
          {featuredEvents.map((_, index) => (
            <button 
              key={index} 
              className={`slider-dot ${currentSlide === index ? 'active' : ''}`}
              onClick={() => setCurrentSlide(index)}
            ></button>
          ))}
        </div>
      </div>

      <div className="premium-events-content">
        <div className="premium-events-intro">
          <h2 className="section-title">Event Coverage Portfolio</h2>
          <p className="section-description">
            Explore my professional sports event coverage across the country. From championship games to press conferences,
            I deliver high-quality broadcasting and multimedia content that captures the excitement and stories of sporting events.
          </p>
        </div>

        {/* Event Category Filter */}
        <div className="premium-events-categories">
          {categories.map((category) => (
            <button
              key={category}
              className={`premium-category-button ${activeCategory === category ? 'active' : ''}`}
              onClick={() => handleCategoryChange(category)}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Events Grid */}
        <div className="premium-events-grid">
          {filteredEvents.map((event, index) => (
            <div
              key={event.id}
              className={`premium-event-card ${animatedItems.includes(index) ? 'animated' : ''}`}
              onClick={() => openEventDetails(event)}
            >
              <div className="premium-event-image" style={{ backgroundImage: `url(${event.imageUrl})` }}>
                <div className="premium-event-overlay">
                  <div className="premium-event-category">{event.category}</div>
                  <div className="premium-event-date">
                    <i className="fas fa-calendar-alt"></i> {event.date}
                  </div>
                </div>
              </div>
              <div className="premium-event-content">
                <h3 className="premium-event-title">{event.title}</h3>
                <p className="premium-event-location">
                  <i className="fas fa-map-marker-alt"></i> {event.location}
                </p>
                <p className="premium-event-excerpt">{event.description.substring(0, 100)}...</p>
                <div className="premium-event-tags">
                  {event.tags.map(tag => (
                    <span key={tag} className="premium-event-tag">{tag}</span>
                  ))}
                </div>
                <div className="premium-event-stats">
                  <div className="premium-stat">
                    <div className="premium-stat-value">{event.stats.attendees}</div>
                    <div className="premium-stat-label">Attendees</div>
                  </div>
                  <div className="premium-stat">
                    <div className="premium-stat-value">{event.stats.broadcastReach}</div>
                    <div className="premium-stat-label">Viewers</div>
                  </div>
                </div>
                <button className="premium-event-button">
                  View Details <i className="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Event Coverage Process */}
        <div className="premium-process-section">
          <h2 className="section-subtitle">My Event Coverage Process</h2>
          <div className="premium-process-steps">
            <div className="premium-process-step">
              <div className="premium-step-number">01</div>
              <div className="premium-step-content">
                <h3 className="premium-step-title">Pre-Event Planning</h3>
                <p className="premium-step-description">
                  Comprehensive preparation including venue assessment, equipment setup, and coordination with event organizers to ensure seamless coverage.
                </p>
              </div>
            </div>
            <div className="premium-process-step">
              <div className="premium-step-number">02</div>
              <div className="premium-step-content">
                <h3 className="premium-step-title">Live Coverage</h3>
                <p className="premium-step-description">
                  Professional broadcasting with play-by-play commentary, expert analysis, and real-time updates across multiple platforms.
                </p>
              </div>
            </div>
            <div className="premium-process-step">
              <div className="premium-step-number">03</div>
              <div className="premium-step-content">
                <h3 className="premium-step-title">Interviews & Features</h3>
                <p className="premium-step-description">
                  In-depth interviews with athletes, coaches, and key figures, along with special feature segments that tell the complete story.
                </p>
              </div>
            </div>
            <div className="premium-process-step">
              <div className="premium-step-number">04</div>
              <div className="premium-step-content">
                <h3 className="premium-step-title">Post-Event Content</h3>
                <p className="premium-step-description">
                  Comprehensive highlight packages, analysis, and follow-up stories that extend the event's reach and impact.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="premium-testimonials-section">
          <h2 className="section-subtitle">Client Testimonials</h2>
          <div className="premium-testimonials">
            <div className="premium-testimonial">
              <div className="premium-testimonial-content">
                <p className="premium-testimonial-text">
                  "Siddartha's coverage of our national championship was exceptional. His professionalism, attention to detail, and ability to capture the essence of the event exceeded our expectations."
                </p>
                <div className="premium-testimonial-author">
                  <div className="premium-author-name">Rajiv Sharma</div>
                  <div className="premium-author-title">Director, National Basketball Federation</div>
                </div>
              </div>
            </div>
            <div className="premium-testimonial">
              <div className="premium-testimonial-content">
                <p className="premium-testimonial-text">
                  "Working with Siddartha on our international cricket series was a pleasure. His comprehensive coverage and engaging presentation style helped us reach a wider audience than ever before."
                </p>
                <div className="premium-testimonial-author">
                  <div className="premium-author-name">Priya Patel</div>
                  <div className="premium-author-title">Media Relations, Cricket Association of India</div>
                </div>
              </div>
            </div>
            <div className="premium-testimonial">
              <div className="premium-testimonial-content">
                <p className="premium-testimonial-text">
                  "Siddartha brought our college tournament to life with his dynamic coverage and storytelling. His ability to highlight emerging talent and capture the excitement of collegiate sports is unmatched."
                </p>
                <div className="premium-testimonial-author">
                  <div className="premium-author-name">Dr. Anand Kumar</div>
                  <div className="premium-author-title">Sports Director, University Athletics Association</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="premium-cta-section">
          <div className="premium-cta-content">
            <h2 className="premium-cta-title">Ready to Elevate Your Event Coverage?</h2>
            <p className="premium-cta-description">
              Let's discuss how I can provide comprehensive media coverage for your upcoming sports event.
            </p>
            <div className="premium-cta-buttons">
              <a href="#contact" className="premium-cta-button primary">
                Get in Touch <i className="fas fa-arrow-right"></i>
              </a>
              <a href="#services" className="premium-cta-button secondary">
                View Services <i className="fas fa-list"></i>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Event Details Modal */}
      {isModalOpen && selectedEvent && (
        <div className="premium-modal" onClick={closeEventDetails}>
          <div className="premium-modal-content" onClick={e => e.stopPropagation()}>
            <button className="premium-modal-close" onClick={closeEventDetails}>
              <i className="fas fa-times"></i>
            </button>
            
            <div className="premium-modal-header">
              <div className="premium-modal-image" style={{ backgroundImage: `url(${selectedEvent.imageUrl})` }}>
                <div className="premium-modal-overlay">
                  <div className="premium-modal-category">{selectedEvent.category}</div>
                  <h2 className="premium-modal-title">{selectedEvent.title}</h2>
                  <div className="premium-modal-meta">
                    <span className="premium-modal-date"><i className="fas fa-calendar-alt"></i> {selectedEvent.date}</span>
                    <span className="premium-modal-location"><i className="fas fa-map-marker-alt"></i> {selectedEvent.location}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="premium-modal-body">
              <div className="premium-modal-section">
                <h3 className="premium-modal-section-title">Event Overview</h3>
                <p className="premium-modal-description">{selectedEvent.description}</p>
              </div>
              
              <div className="premium-modal-stats-section">
                <div className="premium-modal-stat">
                  <div className="premium-modal-stat-value">{selectedEvent.stats.attendees}</div>
                  <div className="premium-modal-stat-label">Attendees</div>
                </div>
                <div className="premium-modal-stat">
                  <div className="premium-modal-stat-value">{selectedEvent.stats.broadcastReach}</div>
                  <div className="premium-modal-stat-label">Broadcast Reach</div>
                </div>
                <div className="premium-modal-stat">
                  <div className="premium-modal-stat-value">{selectedEvent.stats.mediaTeam}</div>
                  <div className="premium-modal-stat-label">Media Team</div>
                </div>
                <div className="premium-modal-stat">
                  <div className="premium-modal-stat-value">{selectedEvent.stats.duration}</div>
                  <div className="premium-modal-stat-label">Duration</div>
                </div>
              </div>
              
              <div className="premium-modal-section">
                <h3 className="premium-modal-section-title">Coverage Highlights</h3>
                <ul className="premium-modal-highlights">
                  {selectedEvent.highlights.map((highlight, index) => (
                    <li key={index} className="premium-highlight-item">
                      <i className="fas fa-check-circle"></i> {highlight}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="premium-modal-section">
                <h3 className="premium-modal-section-title">Event Gallery</h3>
                <div className="premium-modal-gallery">
                  {selectedEvent.gallery.map((image, index) => (
                    <div key={index} className="premium-gallery-item">
                      <img src={image} alt={`${selectedEvent.title} - Image ${index + 1}`} className="premium-gallery-image" />
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="premium-modal-tags">
                {selectedEvent.tags.map(tag => (
                  <span key={tag} className="premium-modal-tag">{tag}</span>
                ))}
              </div>
              
              <div className="premium-modal-cta">
                <a href="#contact" className="premium-modal-cta-button">
                  Request Similar Coverage <i className="fas fa-arrow-right"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default PremiumEventsSection;
