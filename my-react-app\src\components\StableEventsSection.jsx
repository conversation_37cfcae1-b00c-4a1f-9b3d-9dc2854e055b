import React, { useState, useEffect } from 'react';
import './Sections.css';
import './StableEventsSection.css';

const StableEventsSection = () => {
  const [activeCategory, setActiveCategory] = useState('All');
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Event data with event images
  const eventItems = [
    {
      id: 1,
      title: 'National Basketball Championship Finals',
      date: 'June 15, 2024',
      location: 'National Stadium, Delhi',
      description: 'Comprehensive coverage of the national basketball championship finals, featuring the country\'s top teams competing for the prestigious title. This event includes pre-game analysis, live commentary, post-game interviews with players and coaches, and in-depth highlight packages.',
      imageUrl: '/images/events/event1.jpg',
      category: 'Championship',
      tags: ['Live Coverage', 'Basketball', 'National'],
      highlights: [
        'Live play-by-play commentary throughout the championship game',
        'Exclusive interviews with MVP players and coaching staff',
        'Comprehensive statistical analysis and game breakdowns',
        'Behind-the-scenes footage of team preparations and celebrations'
      ]
    },
    {
      id: 2,
      title: 'India vs Australia Cricket Series',
      date: 'July 22, 2024',
      location: 'Cricket Stadium, Mumbai',
      description: 'Full media coverage of the international cricket series between India and Australia, including match commentary, player interviews, and expert analysis. This high-profile event attracts global attention and features comprehensive digital and broadcast content production.',
      imageUrl: '/images/events/event2.jpg',
      category: 'International',
      tags: ['Cricket', 'Series Coverage', 'International'],
      highlights: [
        'Ball-by-ball commentary for all five test matches',
        'Daily recap shows with expert panel discussions',
        'Player profile features and career retrospectives',
        'Technical analysis segments with former international players'
      ]
    },
    {
      id: 3,
      title: 'All-India College Football Tournament',
      date: 'August 10, 2024',
      location: 'University Stadium, Bangalore',
      description: 'Coverage of the annual inter-college football tournament featuring the top university teams from across the country. This event showcases emerging talent and includes feature stories on promising young athletes, team strategies, and the competitive collegiate sports landscape.',
      imageUrl: '/images/events/event3.jpg',
      category: 'Tournament',
      tags: ['Football', 'College Sports', 'Tournament'],
      highlights: [
        'Coverage of all 32 tournament matches across three weeks',
        'Special segments on rising stars and NFL prospects',
        'Tactical analysis of innovative coaching strategies',
        'Documentary-style features on team rivalries and traditions'
      ]
    },
    {
      id: 4,
      title: 'Olympic Trials Media Day',
      date: 'September 5, 2024',
      location: 'Olympic Training Center, Delhi',
      description: 'Media coverage of the Olympic trials press conference, featuring interviews with athletes, coaches, and Olympic committee members. This event provides insights into the selection process, athlete preparations, and expectations for the upcoming Olympic Games.',
      imageUrl: '/images/events/event4.jpg',
      category: 'Press Conference',
      tags: ['Olympics', 'Press Coverage', 'Interviews'],
      highlights: [
        'Exclusive one-on-one interviews with Olympic hopefuls',
        'In-depth coverage of selection criteria and qualification standards',
        'Feature stories on athletes\' training regimens and personal journeys',
        'Expert analysis of India\'s medal prospects across various sports'
      ]
    },
    {
      id: 5,
      title: 'South Asian Athletics Championship',
      date: 'October 18, 2024',
      location: 'Sports Complex, Chennai',
      description: 'Complete coverage of the regional athletics championship, featuring track and field events, swimming competitions, and team sports. This multi-day event showcases athletic talent from across the region and serves as a qualifier for national championships.',
      imageUrl: '/images/events/event5.jpg',
      category: 'Championship',
      tags: ['Athletics', 'Regional', 'Multi-sport'],
      highlights: [
        'Live coverage of all final events across 12 sports disciplines',
        'Daily highlight packages and medal ceremony coverage',
        'Interviews with regional sports administrators and coaches',
        'Special features on community impact and sports development initiatives'
      ]
    },
    {
      id: 6,
      title: 'National Sports Awards Gala',
      date: 'November 30, 2024',
      location: 'Grand Hotel, Delhi',
      description: 'Coverage of the annual sports awards ceremony honoring outstanding athletes, coaches, and teams from the past year. This prestigious event includes red carpet interviews, acceptance speeches, and special feature segments on the year\'s most memorable sporting moments.',
      imageUrl: '/images/events/event1.jpg',
      category: 'Ceremony',
      tags: ['Awards', 'Gala', 'Recognition'],
      highlights: [
        'Red carpet interviews with all major award nominees and presenters',
        'Live broadcast of the entire three-hour ceremony',
        'Behind-the-scenes access to preparation and rehearsals',
        'Retrospective segments celebrating the year\'s sporting achievements'
      ]
    }
  ];

  // Get unique categories
  const categories = ['All', ...new Set(eventItems.map(event => event.category))];

  // Filter events based on active category
  const filteredEvents = activeCategory === 'All'
    ? eventItems
    : eventItems.filter(event => event.category === activeCategory);

  // Open event details modal
  const openEventDetails = (event) => {
    setSelectedEvent(event);
    setIsModalOpen(true);
    document.body.style.overflow = 'hidden';
  };

  // Close event details modal
  const closeEventDetails = () => {
    setIsModalOpen(false);
    document.body.style.overflow = 'auto';
  };

  // Handle category change
  const handleCategoryChange = (category) => {
    setActiveCategory(category);
  };

  // Close modal when ESC key is pressed
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.keyCode === 27) {
        closeEventDetails();
      }
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, []);

  return (
    <section className="section stable-events-section" id="events">
      {/* Hero Banner */}
      <div className="stable-hero-banner">
        <div className="stable-hero-content">
          <h1 className="stable-hero-title">Event Coverage</h1>
          <p className="stable-hero-subtitle">Professional sports event coverage across the country</p>
        </div>
      </div>

      <div className="stable-events-content">
        <div className="stable-events-intro">
          <h2 className="section-title">Event Portfolio</h2>
          <p className="section-description">
            Explore my professional sports event coverage across the country. From championship games to press conferences,
            I deliver high-quality broadcasting and multimedia content that captures the excitement and stories of sporting events.
          </p>
        </div>

        {/* Event Category Filter */}
        <div className="stable-events-categories">
          {categories.map((category) => (
            <button
              key={category}
              className={`stable-category-button ${activeCategory === category ? 'active' : ''}`}
              onClick={() => handleCategoryChange(category)}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Events Grid */}
        <div className="stable-events-grid">
          {filteredEvents.map((event) => (
            <div
              key={event.id}
              className="stable-event-card"
              onClick={() => openEventDetails(event)}
            >
              <div className="stable-event-image">
                <img src={event.imageUrl} alt={event.title} />
                <div className="stable-event-overlay">
                  <div className="stable-event-category">{event.category}</div>
                </div>
              </div>
              <div className="stable-event-content">
                <h3 className="stable-event-title">{event.title}</h3>
                <p className="stable-event-meta">
                  <span className="stable-event-date"><i className="fas fa-calendar-alt"></i> {event.date}</span>
                  <span className="stable-event-location"><i className="fas fa-map-marker-alt"></i> {event.location}</span>
                </p>
                <p className="stable-event-excerpt">{event.description.substring(0, 120)}...</p>
                <div className="stable-event-tags">
                  {event.tags.map(tag => (
                    <span key={tag} className="stable-event-tag">{tag}</span>
                  ))}
                </div>
                <button className="stable-event-button">
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Services Section */}
        <div className="stable-services-section">
          <h2 className="section-subtitle">Event Coverage Services</h2>
          <div className="stable-services-grid">
            <div className="stable-service-card">
              <div className="stable-service-icon">
                <i className="fas fa-microphone"></i>
              </div>
              <h3 className="stable-service-title">Live Broadcasting</h3>
              <p className="stable-service-description">
                Professional live commentary and play-by-play coverage for sporting events of all sizes.
              </p>
            </div>
            <div className="stable-service-card">
              <div className="stable-service-icon">
                <i className="fas fa-camera"></i>
              </div>
              <h3 className="stable-service-title">Event Photography</h3>
              <p className="stable-service-description">
                High-quality sports photography capturing the key moments and emotions of your event.
              </p>
            </div>
            <div className="stable-service-card">
              <div className="stable-service-icon">
                <i className="fas fa-video"></i>
              </div>
              <h3 className="stable-service-title">Video Production</h3>
              <p className="stable-service-description">
                Complete video production services including highlight reels, interviews, and feature segments.
              </p>
            </div>
            <div className="stable-service-card">
              <div className="stable-service-icon">
                <i className="fas fa-newspaper"></i>
              </div>
              <h3 className="stable-service-title">Media Coverage</h3>
              <p className="stable-service-description">
                Comprehensive media coverage including press conferences, interviews, and written content.
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="stable-cta-section">
          <div className="stable-cta-content">
            <h2 className="stable-cta-title">Need Professional Coverage for Your Next Event?</h2>
            <p className="stable-cta-description">
              Let's discuss how I can provide comprehensive media coverage for your upcoming sports event.
            </p>
            <a href="#contact" className="stable-cta-button">
              Get in Touch
            </a>
          </div>
        </div>
      </div>

      {/* Event Details Modal */}
      {isModalOpen && selectedEvent && (
        <div className="stable-modal" onClick={closeEventDetails}>
          <div className="stable-modal-content" onClick={e => e.stopPropagation()}>
            <button className="stable-modal-close" onClick={closeEventDetails}>
              <i className="fas fa-times"></i>
            </button>
            
            <div className="stable-modal-header">
              <img src={selectedEvent.imageUrl} alt={selectedEvent.title} className="stable-modal-image" />
              <div className="stable-modal-header-content">
                <div className="stable-modal-category">{selectedEvent.category}</div>
                <h2 className="stable-modal-title">{selectedEvent.title}</h2>
                <div className="stable-modal-meta">
                  <span className="stable-modal-date"><i className="fas fa-calendar-alt"></i> {selectedEvent.date}</span>
                  <span className="stable-modal-location"><i className="fas fa-map-marker-alt"></i> {selectedEvent.location}</span>
                </div>
              </div>
            </div>
            
            <div className="stable-modal-body">
              <div className="stable-modal-section">
                <h3 className="stable-modal-section-title">Event Overview</h3>
                <p className="stable-modal-description">{selectedEvent.description}</p>
              </div>
              
              <div className="stable-modal-section">
                <h3 className="stable-modal-section-title">Coverage Highlights</h3>
                <ul className="stable-modal-highlights">
                  {selectedEvent.highlights.map((highlight, index) => (
                    <li key={index} className="stable-highlight-item">
                      <i className="fas fa-check-circle"></i> {highlight}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="stable-modal-tags">
                {selectedEvent.tags.map(tag => (
                  <span key={tag} className="stable-modal-tag">{tag}</span>
                ))}
              </div>
              
              <div className="stable-modal-cta">
                <a href="#contact" className="stable-modal-cta-button">
                  Request Similar Coverage
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default StableEventsSection;
