/* Simple Events Section Styles */
.simple-events-section {
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
  padding: 0 0 5rem 0;
}

.simple-events-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Banner */
.simple-hero-banner {
  background-color: #1a3a6c;
  color: white;
  padding: 5rem 0;
  margin-bottom: 4rem;
  position: relative;
  text-align: center;
}

.simple-hero-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/events/event3.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.2;
}

.simple-hero-content {
  position: relative;
  z-index: 2;
}

.simple-hero-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.simple-hero-subtitle {
  font-size: 1.2rem;
  font-weight: 400;
  max-width: 700px;
  margin: 0 auto;
}

/* Events Intro */
.simple-events-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1a3a6c;
}

.section-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #555;
}

/* Category Filter */
.simple-events-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.simple-category-button {
  padding: 0.8rem 1.5rem;
  background-color: white;
  color: #333;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.simple-category-button:hover {
  background-color: #f0f0f0;
}

.simple-category-button.active {
  background-color: #1a3a6c;
  color: white;
}

/* Events Grid */
.simple-events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.simple-event-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.simple-event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.simple-event-image {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.simple-event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.simple-event-card:hover .simple-event-image img {
  transform: scale(1.05);
}

.simple-event-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.simple-event-category {
  background-color: #1a3a6c;
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.simple-event-content {
  padding: 1.5rem;
}

.simple-event-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  color: #1a3a6c;
}

.simple-event-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.simple-event-date, .simple-event-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.simple-event-excerpt {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.simple-event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.simple-event-tag {
  background-color: #f0f0f0;
  color: #555;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.simple-event-button {
  background-color: #1a3a6c;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
}

.simple-event-button:hover {
  background-color: #0f2a5c;
}

/* Services Section */
.simple-services-section {
  margin: 5rem 0;
}

.section-subtitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: #1a3a6c;
}

.simple-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
}

.simple-service-card {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.simple-service-card:hover {
  transform: translateY(-5px);
}

.simple-service-icon {
  width: 60px;
  height: 60px;
  background-color: #1a3a6c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.5rem;
}

.simple-service-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1a3a6c;
}

.simple-service-description {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.6;
}

/* Call to Action */
.simple-cta-section {
  margin-top: 5rem;
  background-color: #1a3a6c;
  border-radius: 8px;
  padding: 3rem;
  color: white;
  text-align: center;
}

.simple-cta-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.simple-cta-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.simple-cta-button {
  display: inline-block;
  background-color: white;
  color: #1a3a6c;
  padding: 1rem 2rem;
  border-radius: 4px;
  font-weight: 700;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.simple-cta-button:hover {
  background-color: #f0f0f0;
}

/* Modal Styles */
.simple-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.simple-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 800px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
}

.simple-modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  font-size: 1rem;
}

.simple-modal-header {
  position: relative;
}

.simple-modal-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.simple-modal-header-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.simple-modal-category {
  display: inline-block;
  background-color: #1a3a6c;
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.simple-modal-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.simple-modal-meta {
  display: flex;
  gap: 1.5rem;
  font-size: 0.9rem;
}

.simple-modal-date, .simple-modal-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.simple-modal-body {
  padding: 2rem;
}

.simple-modal-section {
  margin-bottom: 2rem;
}

.simple-modal-section-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1a3a6c;
}

.simple-modal-description {
  color: #555;
  font-size: 1rem;
  line-height: 1.7;
}

.simple-modal-highlights {
  list-style: none;
  padding: 0;
  margin: 0;
}

.simple-highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  color: #555;
  font-size: 1rem;
  line-height: 1.5;
}

.simple-highlight-item i {
  color: #1a3a6c;
  margin-top: 0.2rem;
}

.simple-modal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.simple-modal-tag {
  background-color: #f0f0f0;
  color: #555;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.simple-modal-cta {
  text-align: center;
}

.simple-modal-cta-button {
  display: inline-block;
  background-color: #1a3a6c;
  color: white;
  padding: 1rem 2rem;
  border-radius: 4px;
  font-weight: 700;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.simple-modal-cta-button:hover {
  background-color: #0f2a5c;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .simple-events-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .simple-hero-title {
    font-size: 2.5rem;
  }
  
  .simple-modal-image {
    height: 250px;
  }
  
  .simple-modal-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .simple-events-grid {
    grid-template-columns: 1fr;
  }
  
  .simple-services-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .simple-hero-title {
    font-size: 2rem;
  }
  
  .simple-cta-section {
    padding: 2rem;
  }
  
  .simple-cta-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .simple-services-grid {
    grid-template-columns: 1fr;
  }
  
  .simple-hero-title {
    font-size: 1.8rem;
  }
  
  .simple-modal-header-content {
    padding: 1.5rem;
  }
  
  .simple-modal-title {
    font-size: 1.5rem;
  }
  
  .simple-modal-body {
    padding: 1.5rem;
  }
  
  .simple-modal-image {
    height: 200px;
  }
}
