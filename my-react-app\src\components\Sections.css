.section {
  padding: 8rem 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  scroll-margin-top: 80px; /* Ensures smooth scrolling with sticky navigation */
  overflow: hidden;
}

.section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(14, 59, 125, 0.03), transparent 60%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.03), transparent 60%);
  pointer-events: none;
  z-index: -1;
}

.section-title {
  font-size: 3rem;
  margin-bottom: 3rem;
  text-align: center;
  color: var(--dark-color);
  position: relative;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 1px;
  line-height: 1.2;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-title.visible {
  opacity: 1;
  transform: translateY(0);
}

.section-title::before {
  content: attr(data-text);
  position: absolute;
  left: 50%;
  top: -0.5rem;
  transform: translateX(-50%);
  font-size: 5rem;
  font-weight: 900;
  color: var(--primary-color);
  opacity: 0.03;
  white-space: nowrap;
  z-index: -1;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.section-title::after {
  content: '';
  display: block;
  width: 80px;
  height: 5px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  margin: 1rem auto 0;
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
}

.section-title.visible::after {
  transform: scaleX(1);
}

.section-subtitle {
  font-size: 2rem;
  margin: 4rem 0 2rem;
  color: var(--dark-color);
  position: relative;
  font-weight: 600;
  text-align: center;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-subtitle.visible {
  opacity: 1;
  transform: translateY(0);
}

.section-subtitle::after {
  content: '';
  display: block;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  margin: 0.8rem auto 0;
  opacity: 0.7;
  border-radius: 2px;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
}

.section-subtitle.visible::after {
  transform: scaleX(1);
}

.section-content {
  margin-bottom: 3rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
}

.section-content.visible {
  opacity: 1;
  transform: translateY(0);
}

.section-description {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  color: var(--gray-color);
  font-size: 1.25rem;
  line-height: 1.8;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
}

.section-description.visible {
  opacity: 1;
  transform: translateY(0);
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2.8rem;
  margin-top: 3rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s;
}

.items-grid.visible {
  opacity: 1;
  transform: translateY(0);
}

.item-card {
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transform: translateZ(0);
  backface-visibility: hidden;
  opacity: 0;
  transform: translateY(30px);
}

.item-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
  z-index: 1;
}

.item-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.item-card:hover::before {
  transform: scaleX(1);
}

.item-badge {
  position: absolute;
  top: 1.2rem;
  right: 1.2rem;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  transform: translateZ(0);
}

.item-card:hover .item-badge {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
  background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
}

.item-placeholder {
  background-color: #f5f5f7;
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #777;
  position: relative;
  overflow: hidden;
  transition: all 0.5s ease;
}

.item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(14, 59, 125, 0.7),
    rgba(230, 57, 70, 0.7)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1.1);
  backdrop-filter: blur(3px);
}

.item-card:hover .item-overlay {
  opacity: 1;
  transform: scale(1);
}

.play-button {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.6rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  transform: scale(0.8) translateY(20px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0;
  position: relative;
  overflow: hidden;
}

.play-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.item-card:hover .play-button {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.play-button:hover {
  transform: scale(1.1) !important;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.play-button:hover::before {
  opacity: 1;
}

.item-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(14, 59, 125, 0.05) 0%, rgba(230, 57, 70, 0.05) 100%);
  z-index: 0;
}

.item-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);
  filter: brightness(0.95) contrast(1.05);
}

.item-card:hover .item-placeholder img {
  transform: scale(1.08);
  filter: brightness(1.05) contrast(1.1);
}

.video-placeholder {
  background-color: #f5f5f7;
}

.graphic-placeholder {
  background-color: #f5f5f7;
}

.placeholder-text {
  font-size: 1.2rem;
  font-weight: 500;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.item-content {
  padding: 1.8rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  background: white;
}

.item-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(14, 59, 125, 0.02), transparent 70%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.02), transparent 70%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.item-card:hover .item-content::before {
  opacity: 1;
}

.item-title {
  font-size: 1.5rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.item-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transition: width 0.4s ease;
}

.item-card:hover .item-title {
  color: var(--primary-color);
}

.item-card:hover .item-title::after {
  width: 100%;
}

.item-description {
  color: var(--gray-color);
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 1.8rem;
  flex-grow: 1;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1.2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.item-date {
  color: var(--gray-color);
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.item-date i {
  color: var(--primary-color);
  opacity: 0.7;
}

.item-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.7rem 1.5rem;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-align: center;
  border: none;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 1px;
  box-shadow: 0 4px 10px rgba(14, 59, 125, 0.2);
  position: relative;
  overflow: hidden;
  gap: 0.5rem;
}

.item-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.item-link:hover {
  background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
  color: white;
  text-decoration: none;
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.3);
}

.item-link:hover::before {
  transform: translateX(100%);
}

.item-link i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.item-link:hover i {
  transform: translateX(3px);
}

/* YouTube video container */
.video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-bottom: 1.5rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0);
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.video-featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2.5rem;
  margin: 3rem 0;
}

.video-card {
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.video-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.video-content {
  padding: 1.8rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.video-date {
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  display: block;
}

.video-title {
  font-size: 1.4rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
}

.video-description {
  color: var(--gray-color);
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 1.2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  color: var(--gray-color);
  font-size: 0.95rem;
}

.video-views, .video-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.video-views i, .video-category i {
  color: var(--primary-color);
  opacity: 0.7;
}

.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
  z-index: 1;
  pointer-events: none;
}

.video-container:hover {
  transform: translateY(-10px) scale(1.01);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  z-index: 0;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 3rem;
  margin: 4rem 0;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s;
}

.video-grid.visible {
  opacity: 1;
  transform: translateY(0);
}

.video-item {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  opacity: 0;
  transform: translateY(30px);
}

.video-item.visible {
  opacity: 1;
  transform: translateY(0);
}

.video-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
  z-index: 1;
}

.video-item:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.video-item:hover::before {
  transform: scaleX(1);
}

.video-thumbnail {
  position: relative;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);
  filter: brightness(0.95) contrast(1.05);
}

.video-item:hover .video-thumbnail img {
  transform: scale(1.05);
  filter: brightness(1.05) contrast(1.1);
}

.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(14, 59, 125, 0.5),
    rgba(230, 57, 70, 0.5)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(2px);
}

.video-item:hover .video-play-overlay {
  opacity: 1;
}

.video-play-button {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.6rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  transform: scale(0.8);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.video-play-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.video-item:hover .video-play-button {
  transform: scale(1);
}

.video-play-button:hover {
  transform: scale(1.1) !important;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.video-play-button:hover::before {
  opacity: 1;
}

.video-content {
  padding: 1.8rem;
  background: white;
  position: relative;
  z-index: 1;
}

.video-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(14, 59, 125, 0.02), transparent 70%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.02), transparent 70%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.video-item:hover .video-content::before {
  opacity: 1;
}

.video-title {
  font-size: 1.5rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.video-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transition: width 0.4s ease;
}

.video-item:hover .video-title {
  color: var(--primary-color);
}

.video-item:hover .video-title::after {
  width: 100%;
}

.video-description {
  color: var(--gray-color);
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 1.2rem;
  color: var(--gray-color);
  font-size: 0.95rem;
  padding-top: 1.2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.video-views, .video-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.video-views i, .video-date i {
  color: var(--primary-color);
  opacity: 0.7;
}

/* Section backgrounds */
.broadcast-section {
  background-color: var(--light-color);
  position: relative;
  overflow: hidden;
}

.broadcast-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1504450758481-7338eba7524a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.04;
  z-index: -1;
  filter: blur(1px);
}

.broadcast-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(14, 59, 125, 0.05), transparent 60%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.05), transparent 60%);
  z-index: -1;
}

/* Broadcast CTA styles */
.broadcast-cta {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 4rem;
  border-radius: var(--border-radius-lg);
  margin-top: 5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 3rem;
  box-shadow: 0 20px 50px rgba(14, 59, 125, 0.3);
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  backface-visibility: hidden;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.broadcast-cta.visible {
  opacity: 1;
  transform: translateY(0);
}

.broadcast-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

.broadcast-cta::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.2) 0%, transparent 100%);
  z-index: 0;
  opacity: 0.5;
}

.cta-content {
  flex: 1;
  position: relative;
  z-index: 1;
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
}

.broadcast-cta.visible .cta-content {
  opacity: 1;
  transform: translateX(0);
}

.broadcast-cta h3 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 800;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
}

.broadcast-cta h3::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.broadcast-cta p {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  max-width: 600px;
  opacity: 0.9;
  line-height: 1.7;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-buttons {
  display: flex;
  gap: 1.2rem;
  flex-wrap: wrap;
}

.cta-buttons .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.cta-image {
  width: 320px;
  height: 320px;
  border-radius: 50%;
  overflow: hidden;
  border: 8px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  display: none;
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s;
}

.broadcast-cta.visible .cta-image {
  opacity: 1;
  transform: translateX(0);
}

.cta-image::before {
  content: '';
  position: absolute;
  top: -15px;
  left: -15px;
  right: -15px;
  bottom: -15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: rotate 30s linear infinite;
  pointer-events: none;
  z-index: -1;
}

.cta-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
  filter: contrast(1.1) saturate(1.1);
}

.cta-image:hover img {
  transform: scale(1.05);
}

@media (min-width: 992px) {
  .cta-image {
    display: block;
  }

  .broadcast-cta {
    text-align: left;
  }
}

@media (max-width: 991px) {
  .broadcast-cta {
    text-align: center;
    padding: 2.5rem;
  }

  .cta-buttons {
    justify-content: center;
  }
}

.photography-section {
  background-color: #ffffff;
}

.videography-section {
  background-color: var(--light-color);
  position: relative;
}

.videography-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.03;
  z-index: -1;
}

.graphic-design-section {
  background-color: #ffffff;
}

/* Section dividers */
.section-divider {
  height: 120px;
  width: 100%;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 1;
}

.section-divider.wave {
  background-color: transparent;
}

.section-divider.wave .divider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23f1faee'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23f1faee'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23f1faee'%3E%3C/path%3E%3C/svg%3E") no-repeat;
  background-size: cover;
  background-position: center;
  animation: float 6s ease-in-out infinite;
  transform-origin: center;
}

.section-divider.wave::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(241, 250, 238, 0), rgba(241, 250, 238, 0.5));
  z-index: -1;
}

.section-divider.angle {
  background-color: transparent;
  height: 150px;
}

.section-divider.angle .divider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  background: linear-gradient(to bottom right, transparent 49.5%, var(--light-color) 50%);
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.03);
}

.section-divider.angle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.05), transparent);
}

.section-divider.curve {
  background-color: transparent;
  height: 100px;
}

.section-divider.curve .divider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M600,112.77C268.63,112.77,0,65.52,0,7.23V120H1200V7.23C1200,65.52,931.37,112.77,600,112.77Z' fill='%23f1faee'%3E%3C/path%3E%3C/svg%3E") no-repeat;
  background-size: cover;
  background-position: center;
}

.section-divider.tilt {
  background-color: transparent;
  height: 100px;
}

.section-divider.tilt .divider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M1200 120L0 16.48 0 0 1200 0 1200 120z' fill='%23f1faee'%3E%3C/path%3E%3C/svg%3E") no-repeat;
  background-size: cover;
  background-position: center;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .section {
    padding: 4rem 1rem;
  }

  .video-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .items-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
  }

  .item-placeholder {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .section {
    padding: 3rem 1rem;
  }

  .items-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 1.8rem;
  }
}

/* Photography section styles */
.photo-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.category-button {
  padding: 0.7rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: 2px solid var(--primary-color);
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
}

.category-button:hover {
  background-color: transparent;
  color: var(--primary-color);
}

.photo-category-section {
  margin-bottom: 4rem;
  scroll-margin-top: 100px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.photo-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  height: 250px;
  position: relative;
}

.photo-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.photo-thumbnail {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 1.5rem 1rem 1rem;
  opacity: 0;
  transition: var(--transition);
}

.photo-item:hover .photo-overlay {
  opacity: 1;
}

.photo-title {
  color: white;
  font-size: 1.1rem;
  margin: 0;
}

/* Lightbox styles */
.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 2rem;
}

.lightbox-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1001;
}

.lightbox-content {
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.lightbox-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.lightbox-details {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0 0 8px 8px;
  width: 100%;
  max-width: 800px;
  margin-top: 1rem;
}

.lightbox-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.lightbox-description {
  color: var(--gray-color);
  font-size: 1rem;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .photo-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .lightbox-content {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .photo-grid {
    grid-template-columns: 1fr;
  }

  .category-button {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}

/* Videography section styles */
.video-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.category-button.active {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
}

.video-featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2.5rem;
  margin: 3rem 0;
}

.video-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
  height: 240px;
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
}

.video-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  transition: var(--transition);
}

.video-card:hover .video-thumbnail::before {
  background: rgba(0, 0, 0, 0.5);
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  opacity: 0.9;
  transition: var(--transition);
}

.video-card:hover .video-play-button {
  transform: translate(-50%, -50%) scale(1.1);
  opacity: 1;
}

.video-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.video-date {
  color: var(--gray-color);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.video-title {
  font-size: 1.4rem;
  color: var(--dark-color);
  margin-bottom: 0.8rem;
  font-weight: 600;
}

.video-description {
  color: var(--gray-color);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.video-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-weight: 600;
  transition: var(--transition);
  align-self: flex-start;
}

.video-link:hover {
  color: var(--secondary-color);
}

.video-link i {
  font-size: 0.8rem;
  transition: var(--transition);
}

.video-link:hover i {
  transform: translateX(3px);
}

/* Video services section */
.video-services {
  margin-top: 5rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2.5rem;
}

.service-card {
  background-color: white;
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-icon {
  width: 70px;
  height: 70px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  margin: 0 auto 1.5rem;
}

.service-title {
  font-size: 1.2rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 600;
}

.service-description {
  color: var(--gray-color);
  font-size: 0.95rem;
  line-height: 1.6;
}

@media (max-width: 992px) {
  .video-featured-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .video-featured-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 480px) {
  .services-grid {
    grid-template-columns: 1fr;
  }
}

/* Graphic Design section styles */
.graphic-projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 3rem 0;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
}

.graphic-projects-grid.visible {
  opacity: 1;
  transform: translateY(0);
}

.graphic-project-card {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  cursor: pointer;
  height: 280px;
  opacity: 0;
  transform: translateY(20px);
  transition: transform 0.4s ease, box-shadow 0.4s ease, opacity 0.4s ease;
}

.graphic-project-card.loaded {
  opacity: 1;
  transform: translateY(0);
}

.project-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.project-image-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.project-image-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(14, 59, 125, 0.2);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.graphic-project-card:hover .project-card-inner {
  transform: scale(1.03);
}

.graphic-project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.project-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  transition: transform 0.5s ease;
}

.graphic-project-card:hover .project-image {
  transform: scale(1.05);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 100%);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 1.5rem;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.graphic-project-card:hover .project-overlay {
  opacity: 1;
}

.project-category {
  display: inline-block;
  background-color: rgba(230, 57, 70, 0.8);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.8rem;
  padding: 0.4rem 0.8rem;
  border-radius: 50px;
  transform: translateY(10px);
  opacity: 0;
  transition: transform 0.4s ease 0.1s, opacity 0.4s ease 0.1s;
}

.graphic-project-card:hover .project-category {
  transform: translateY(0);
  opacity: 1;
}

.project-title {
  color: white;
  font-size: 1.3rem;
  margin-bottom: 1.2rem;
  font-weight: 600;
  transform: translateY(10px);
  opacity: 0;
  transition: transform 0.4s ease 0.2s, opacity 0.4s ease 0.2s;
}

.graphic-project-card:hover .project-title {
  transform: translateY(0);
  opacity: 1;
}

.view-project-btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 0.7rem 1.2rem;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transform: translateY(10px);
  opacity: 0;
  transition: transform 0.4s ease 0.3s, opacity 0.4s ease 0.3s, background-color 0.3s ease;
}

.graphic-project-card:hover .view-project-btn {
  transform: translateY(0);
  opacity: 1;
}

.view-project-btn i {
  transition: transform 0.3s ease;
}

.view-project-btn:hover {
  background-color: white;
  color: var(--secondary-color);
}

.view-project-btn:hover i {
  transform: translateX(3px);
}

/* Design services */
.graphic-design-services {
  margin-top: 4rem;
  padding-top: 2.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
}

.graphic-design-services.visible {
  opacity: 1;
  transform: translateY(0);
}

.design-services-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2.5rem;
}

.design-service {
  background-color: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(20px);
  position: relative;
  overflow: hidden;
  text-align: center;
}

.design-service.visible {
  opacity: 1;
  transform: translateY(0);
}

.design-service::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.design-service:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.design-service:hover::before {
  transform: scaleX(1);
}

.service-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.8rem;
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.2);
  transition: all 0.4s ease;
}

.design-service:hover .service-icon {
  transform: scale(1.1);
  box-shadow: 0 10px 25px rgba(14, 59, 125, 0.3);
}

.design-service h4 {
  color: var(--dark-color);
  font-size: 1.3rem;
  margin-bottom: 1rem;
  position: relative;
  padding-bottom: 0.8rem;
  font-weight: 600;
}

.design-service h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transition: width 0.3s ease;
}

.design-service:hover h4::after {
  width: 60px;
}

.design-service p {
  color: var(--gray-color);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Project modal */
.project-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 2rem;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.project-modal-content {
  background-color: white;
  border-radius: 15px;
  max-width: 1000px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: scaleIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background-color: var(--secondary-color);
  transform: rotate(90deg);
}

.project-modal-image {
  width: 100%;
  height: 400px;
  overflow: hidden;
  position: relative;
}

.project-modal-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
  pointer-events: none;
}

.project-modal-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  transition: transform 0.5s ease;
}

.project-modal-details {
  padding: 2.5rem;
}

.project-modal-title {
  font-size: 2rem;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.project-modal-category {
  margin-bottom: 1.5rem;
}

.project-modal-category span {
  display: inline-block;
  background-color: var(--secondary-color);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 0.4rem 1rem;
  border-radius: 50px;
}

.project-modal-description {
  color: var(--gray-color);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.project-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 2rem;
}

.project-info-item h4 {
  font-size: 1.1rem;
  color: var(--dark-color);
  margin-bottom: 0.8rem;
  font-weight: 600;
}

.project-info-item p {
  color: var(--gray-color);
}

.project-info-item ul {
  list-style: none;
  padding: 0;
}

.project-info-item li {
  color: var(--gray-color);
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.2rem;
}

.project-info-item li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--secondary-color);
}

.project-challenge-solution {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.5rem;
}

.project-section h4 {
  font-size: 1.2rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 600;
}

.project-section p {
  color: var(--gray-color);
  line-height: 1.6;
}

@media (max-width: 992px) {
  .graphic-projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.2rem;
  }

  .project-challenge-solution {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .design-services-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .graphic-project-card {
    height: 250px;
  }

  .project-modal-image {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .graphic-projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
  }

  .design-services-list {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }

  .project-modal-image {
    height: 280px;
  }

  .project-modal-details {
    padding: 1.5rem;
  }

  .project-info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .graphic-project-card {
    height: 220px;
  }

  .project-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .view-project-btn {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }

  .project-category {
    font-size: 0.7rem;
    padding: 0.3rem 0.7rem;
  }

  .service-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .design-service h4 {
    font-size: 1.2rem;
  }

  .design-service {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .graphic-projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }

  .graphic-project-card {
    height: 180px;
  }

  .project-modal-content {
    width: 95%;
  }

  .project-modal-image {
    height: 200px;
  }

  .project-overlay {
    padding: 1rem;
  }

  .project-title {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .view-project-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.75rem;
  }

  .project-modal-title {
    font-size: 1.5rem;
  }

  .project-modal-description {
    font-size: 0.95rem;
  }

  .project-info-item h4 {
    font-size: 1rem;
  }

  .project-section h4 {
    font-size: 1.1rem;
  }

  .project-section p {
    font-size: 0.95rem;
  }
}

@media (max-width: 380px) {
  .graphic-projects-grid {
    grid-template-columns: 1fr;
  }

  .graphic-project-card {
    height: 220px;
  }
}

/* Broadcast experience timeline styles */
.broadcast-experience {
  margin: 5rem 0;
  padding: 2rem 0;
  position: relative;
}

.experience-timeline {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 0;
}

.experience-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50px;
  width: 4px;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  opacity: 0.3;
}

.experience-item {
  position: relative;
  margin-bottom: 3rem;
  padding-left: 100px;
  opacity: 0;
  transform: translateX(-20px);
  animation: slideInLeft 0.5s ease-out forwards;
}

.experience-item:nth-child(1) {
  animation-delay: 0.1s;
}

.experience-item:nth-child(2) {
  animation-delay: 0.3s;
}

.experience-item:nth-child(3) {
  animation-delay: 0.5s;
}

.experience-item:last-child {
  margin-bottom: 0;
}

.experience-marker {
  position: absolute;
  left: 42px;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--secondary-color);
  border: 4px solid var(--light-color);
  box-shadow: 0 0 0 4px rgba(230, 57, 70, 0.2);
  transition: var(--transition);
  z-index: 1;
}

.experience-item:hover .experience-marker {
  transform: scale(1.2);
  background-color: var(--primary-color);
}

.experience-content {
  background-color: white;
  padding: 1.8rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  border-left: 4px solid var(--secondary-color);
}

.experience-item:hover .experience-content {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-hover);
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.experience-title {
  font-size: 1.4rem;
  color: var(--dark-color);
  font-weight: 700;
  margin: 0;
}

.experience-period {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  background-color: var(--secondary-color);
  padding: 0.3rem 0.8rem;
  border-radius: 50px;
}

.experience-organization {
  color: var(--primary-color);
  font-size: 1.1rem;
  margin: 0 0 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.experience-organization::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.experience-description {
  color: var(--gray-color);
  line-height: 1.7;
  margin: 0;
  font-size: 1.05rem;
}

/* This section was moved to line ~315 */

@media (max-width: 768px) {
  .experience-timeline::before {
    left: 30px;
  }

  .experience-item {
    padding-left: 70px;
  }

  .experience-marker {
    left: 22px;
  }

  .experience-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .experience-period {
    margin-top: 0.5rem;
  }

  .broadcast-cta {
    padding: 2rem;
  }

  .broadcast-cta h3 {
    font-size: 1.5rem;
  }

  .broadcast-cta p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .experience-timeline {
    padding: 1rem 0;
  }

  .experience-item {
    padding-left: 50px;
  }

  .experience-marker {
    width: 16px;
    height: 16px;
    left: 22px;
  }

  .broadcast-cta {
    padding: 1.5rem;
  }
}

/* Contact section styles */
.contact-section {
  background-color: var(--light-color);
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.03;
  z-index: -1;
}

.contact-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-top: 3rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-card {
  background-color: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.contact-card h3 {
  font-size: 1.2rem;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.contact-card p {
  color: var(--gray-color);
}

.contact-social {
  background-color: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.contact-social h3 {
  font-size: 1.2rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icon {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: var(--transition);
}

.social-icon:hover {
  background-color: var(--secondary-color);
  transform: translateY(-3px);
}

.contact-form-container {
  background-color: white;
  padding: 2.5rem;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
  padding: 0.8rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  font-family: inherit;
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(14, 59, 125, 0.1);
}

.submit-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 5px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.submit-button:hover {
  background-color: var(--secondary-color);
}

.form-success-message {
  text-align: center;
  padding: 2rem;
}

.success-icon {
  font-size: 4rem;
  color: #4CAF50;
  margin-bottom: 1.5rem;
}

.form-success-message h3 {
  font-size: 1.8rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.form-success-message p {
  color: var(--gray-color);
  font-size: 1.1rem;
}

@media (max-width: 992px) {
  .contact-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .contact-social {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .contact-info {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 480px) {
  .contact-info {
    grid-template-columns: 1fr;
  }

  .contact-form-container {
    padding: 1.5rem;
  }
}

/* Events section styles */
.events-section {
  background-color: var(--light-color);
  position: relative;
  overflow: hidden;
}

.events-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.03;
  z-index: -1;
  animation: subtle-zoom 30s infinite alternate ease-in-out;
}

.section-background-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  opacity: 0.2;
  animation: float-particle 15s infinite ease-in-out;
}

@keyframes float-particle {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.2;
  }
  25% {
    transform: translate(50px, 25px) scale(1.5);
    opacity: 0.3;
  }
  50% {
    transform: translate(100px, 0) scale(1);
    opacity: 0.2;
  }
  75% {
    transform: translate(50px, -25px) scale(0.8);
    opacity: 0.1;
  }
}

@keyframes subtle-zoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

.event-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s;
}

.event-categories.visible {
  opacity: 1;
  transform: translateY(0);
}

.category-button {
  position: relative;
  padding: 0.8rem 1.8rem;
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
  overflow: hidden;
  z-index: 1;
  animation: fadeIn 0.5s backwards;
}

.category-button-text {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.category-button-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  z-index: 1;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.category-button:hover {
  color: white;
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(14, 59, 125, 0.15);
}

.category-button:hover .category-button-background {
  transform: translateY(0);
}

.category-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 8px 16px rgba(14, 59, 125, 0.2);
}

.category-button.active .category-button-background {
  transform: translateY(0);
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2.5rem;
  margin: 3rem 0;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s;
}

.events-grid.visible {
  opacity: 1;
  transform: translateY(0);
}

.event-card {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  height: 100%;
  cursor: pointer;
  perspective: 1000px;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.event-card.animated {
  opacity: 1;
  transform: translateY(0);
}

.event-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.event-card:hover .event-card-inner {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.event-image {
  height: 220px;
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
  transition: all 0.5s ease;
}

.event-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(14, 59, 125, 0.2), rgba(230, 57, 70, 0.2));
  z-index: 1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.event-card:hover .event-image::before {
  opacity: 1;
}

.event-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.2rem;
  color: white;
  z-index: 2;
  transition: all 0.5s ease;
}

.event-card:hover .event-overlay {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.event-category {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  align-self: flex-start;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  transform: translateZ(10px);
}

.event-card:hover .event-category {
  transform: translateZ(20px) translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.event-date {
  font-size: 0.95rem;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-date::before {
  content: '\f073';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: var(--secondary-color);
}

.event-content {
  padding: 1.8rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background: white;
  z-index: 1;
}

.event-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(14, 59, 125, 0.03), transparent 70%),
              radial-gradient(circle at bottom left, rgba(230, 57, 70, 0.03), transparent 70%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.event-card:hover .event-content::before {
  opacity: 1;
}

.event-title {
  font-size: 1.5rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-weight: 700;
  line-height: 1.3;
  position: relative;
  transition: all 0.3s ease;
}

.event-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  transition: width 0.4s ease;
}

.event-card:hover .event-title {
  color: var(--primary-color);
}

.event-card:hover .event-title::after {
  width: 100%;
}

.event-location {
  color: var(--gray-color);
  font-size: 0.95rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-location i {
  color: var(--primary-color);
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.event-tag {
  background-color: rgba(14, 59, 125, 0.1);
  color: var(--primary-color);
  padding: 0.3rem 0.8rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.event-card:hover .event-tag {
  background-color: rgba(14, 59, 125, 0.15);
  transform: translateY(-2px);
}

.event-excerpt {
  color: var(--gray-color);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.event-details-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  align-self: flex-start;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.event-details-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  z-index: -1;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.event-details-btn:hover {
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(14, 59, 125, 0.15);
}

.event-details-btn:hover::before {
  transform: scaleX(1);
  transform-origin: left;
}

.event-details-btn i {
  transition: transform 0.3s ease;
}

.event-details-btn:hover i {
  transform: translateX(3px);
}

/* Service cards styles */
.event-services {
  margin-top: 5rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.event-services.visible {
  opacity: 1;
  transform: translateY(0);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2.5rem;
  margin-top: 2.5rem;
}

.service-card {
  background-color: white;
  border-radius: 15px;
  padding: 2.5rem 2rem;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
}

.service-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.5s ease;
}

.service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  margin: 0 auto 1.8rem;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.service-icon-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: inherit;
  filter: blur(15px);
  opacity: 0.3;
  z-index: -1;
  transition: all 0.5s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

.service-card:hover .service-icon-glow {
  opacity: 0.5;
  filter: blur(20px);
}

.service-icon.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.service-icon.float {
  animation: float 3s ease-in-out infinite;
}

.service-icon.rotate i {
  animation: rotate 10s linear infinite;
}

.service-title {
  font-size: 1.4rem;
  color: var(--dark-color);
  margin-bottom: 1.2rem;
  font-weight: 700;
  position: relative;
  display: inline-block;
}

.service-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  transition: width 0.4s ease;
}

.service-card:hover .service-title::after {
  width: 60px;
}

.service-description {
  color: var(--gray-color);
  font-size: 1rem;
  line-height: 1.7;
}

.service-card-shine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  transition: transform 0.8s ease;
  pointer-events: none;
}

.service-card:hover .service-card-shine {
  transform: translateX(100%);
}

/* Event modal styles */
.event-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 2rem;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease-out;
}

.event-modal-content {
  background-color: white;
  border-radius: 20px;
  max-width: 1000px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
  animation: scaleIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0);
  backface-visibility: hidden;
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: transparent;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  overflow: hidden;
}

.modal-close span {
  position: relative;
  z-index: 2;
}

.modal-close-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
  z-index: 1;
}

.modal-close:hover {
  transform: rotate(90deg);
}

.modal-close:hover .modal-close-bg {
  transform: scale(1);
  background: var(--secondary-color);
}

.event-modal-image {
  width: 100%;
  height: 450px;
  position: relative;
  overflow: hidden;
}

.event-modal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.5s ease;
  animation: subtle-zoom 20s infinite alternate ease-in-out;
}

.event-modal-details-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 3rem 2.5rem 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.event-modal-category {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
  padding: 0.7rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s 0.3s backwards;
}

.event-modal-date {
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s 0.4s backwards;
}

.event-modal-date::before {
  content: '\f073';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: var(--secondary-color);
}

.event-modal-details {
  padding: 3rem;
  animation: fadeIn 0.5s 0.2s backwards;
}

.event-modal-title {
  font-size: 2.5rem;
  color: var(--dark-color);
  margin-bottom: 1.2rem;
  font-weight: 800;
  line-height: 1.2;
  position: relative;
  display: inline-block;
}

.event-modal-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  border-radius: 2px;
}

.event-modal-location {
  color: var(--gray-color);
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.event-modal-location i {
  color: var(--primary-color);
  font-size: 1.3rem;
}

.event-modal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 2rem;
}

.event-modal-tag {
  background-color: rgba(14, 59, 125, 0.1);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.event-modal-tag:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(14, 59, 125, 0.2);
}

.event-modal-description {
  color: var(--gray-color);
  font-size: 1.15rem;
  line-height: 1.7;
  margin-bottom: 3rem;
}

.event-modal-gallery h3 {
  font-size: 1.8rem;
  color: var(--dark-color);
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.event-modal-gallery h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color));
  border-radius: 2px;
}

.event-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.2rem;
}

.event-gallery-item {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 150px;
  position: relative;
}

.event-gallery-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.event-gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.event-gallery-item:hover .event-gallery-image {
  transform: scale(1.1);
}

@media (max-width: 992px) {
  .events-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .event-modal-image {
    height: 350px;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .event-modal-details {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .events-grid {
    grid-template-columns: 1fr;
  }

  .event-modal-image {
    height: 300px;
  }

  .event-modal-details {
    padding: 1.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .category-button {
    padding: 0.7rem 1.5rem;
    font-size: 0.8rem;
  }

  .event-card-inner {
    transform: none !important;
  }

  .event-modal-title {
    font-size: 2rem;
  }

  .event-modal-gallery h3 {
    font-size: 1.5rem;
  }

  .event-gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .event-modal-content {
    width: 95%;
  }

  .event-modal-image {
    height: 200px;
  }

  .event-modal-details-overlay {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .event-categories {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }

  .category-button {
    width: 100%;
    max-width: 250px;
  }

  .event-card {
    transform: none !important;
  }

  .event-modal-title {
    font-size: 1.8rem;
  }

  .event-modal-description {
    font-size: 1rem;
  }

  .event-gallery-grid {
    grid-template-columns: 1fr;
  }

  .event-gallery-item {
    height: 180px;
  }

  .event-modal-tags {
    justify-content: center;
  }

  .particle {
    display: none;
  }
}
