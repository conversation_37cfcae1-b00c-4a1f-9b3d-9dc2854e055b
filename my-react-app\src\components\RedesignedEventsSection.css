/* Redesigned Events Section Styles */
.events-section.redesigned {
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
  padding: 0 0 5rem 0;
}

/* Hero Section */
.events-hero {
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  color: white;
  padding: 5rem 0;
  margin-bottom: 4rem;
  position: relative;
  overflow: hidden;
}

.events-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/events/event3.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.15;
  mix-blend-mode: overlay;
}

.events-hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  text-align: center;
}

.events-hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.events-hero-subtitle {
  font-size: 1.2rem;
  font-weight: 400;
  margin-bottom: 2.5rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
}

.events-hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
}

.hero-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: white;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

/* Events Intro */
.events-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
}

/* Category Filter */
.events-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.category-button {
  padding: 0.8rem 1.5rem;
  background-color: white;
  color: var(--dark-color);
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.category-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.category-button.active {
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  color: white;
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.2);
}

/* Events Grid */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 2.5rem;
  margin: 3rem 0;
}

.event-card {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  height: 100%;
  cursor: pointer;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.event-card.animated {
  opacity: 1;
  transform: translateY(0);
}

.event-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.event-card:hover .event-card-inner {
  transform: translateY(-10px);
}

.event-image {
  height: 240px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.event-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.2));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
  color: white;
  transition: all 0.5s ease;
}

.event-card:hover .event-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.3));
}

.event-date {
  background-color: var(--primary-color);
  color: white;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 0.8rem;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  align-self: flex-start;
}

.event-month {
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
}

.event-day {
  font-size: 1.5rem;
  font-weight: 800;
  line-height: 1;
}

.event-category-tag {
  background-color: var(--secondary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  align-self: flex-end;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.event-content {
  padding: 1.8rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.event-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0 0 0.8rem 0;
  color: var(--dark-color);
  line-height: 1.3;
  transition: color 0.3s ease;
}

.event-card:hover .event-title {
  color: var(--primary-color);
}

.event-location {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-location i {
  color: var(--primary-color);
}

.event-excerpt {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.event-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 0.4rem 0.8rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.event-card:hover .event-tag {
  background-color: #e6e6e6;
}

.event-details-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 5px 15px rgba(14, 59, 125, 0.2);
  margin-top: auto;
}

.event-details-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.3);
}

.event-details-btn i {
  transition: transform 0.3s ease;
}

.event-details-btn:hover i {
  transform: translateX(3px);
}

/* Event Services */
.event-services {
  margin-top: 5rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.section-subtitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--dark-color);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-card {
  background-color: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: white;
  font-size: 2rem;
  box-shadow: 0 10px 25px rgba(14, 59, 125, 0.2);
  transition: all 0.4s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(10deg);
}

.service-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.service-description {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.service-link {
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.service-link:hover {
  color: var(--secondary-color);
}

.service-link i {
  transition: transform 0.3s ease;
}

.service-link:hover i {
  transform: translateX(3px);
}

/* Call to Action */
.events-cta {
  margin-top: 5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  border-radius: 16px;
  padding: 4rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.events-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/events/event5.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  mix-blend-mode: overlay;
}

.cta-content {
  position: relative;
  z-index: 2;
  max-width: 700px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.cta-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  background-color: white;
  color: var(--primary-color);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.cta-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.cta-button i {
  transition: transform 0.3s ease;
}

.cta-button:hover i {
  transform: translateX(5px);
}

/* Event Modal */
.event-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.event-modal-content {
  background-color: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-close-btn {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.modal-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.event-modal-header {
  position: relative;
}

.modal-image-container {
  height: 350px;
  position: relative;
  overflow: hidden;
}

.modal-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.event-modal-content:hover .modal-image {
  transform: scale(1.05);
}

.modal-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 3rem 2rem 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.modal-category {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.modal-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.modal-meta {
  display: flex;
  gap: 1.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.modal-date, .modal-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.event-modal-body {
  padding: 2.5rem;
}

.modal-section {
  margin-bottom: 2.5rem;
}

.modal-section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  color: var(--dark-color);
  position: relative;
  padding-bottom: 0.8rem;
}

.modal-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

.modal-description {
  color: #555;
  font-size: 1rem;
  line-height: 1.7;
}

.modal-highlights {
  list-style: none;
  padding: 0;
  margin: 0;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  margin-bottom: 1rem;
  color: #555;
  font-size: 1rem;
  line-height: 1.5;
}

.highlight-item i {
  color: var(--primary-color);
  margin-top: 0.2rem;
}

.modal-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.gallery-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.gallery-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.1);
}

.modal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 2.5rem;
}

.modal-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
}

.modal-cta {
  text-align: center;
  margin-top: 2rem;
}

.modal-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(14, 59, 125, 0.2);
}

.modal-cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(14, 59, 125, 0.3);
}

.modal-cta-button i {
  transition: transform 0.3s ease;
}

.modal-cta-button:hover i {
  transform: translateX(5px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .events-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 992px) {
  .events-hero-title {
    font-size: 2.8rem;
  }

  .events-hero-stats {
    gap: 2rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .events-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .modal-image-container {
    height: 300px;
  }

  .modal-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .events-hero {
    padding: 4rem 0;
  }

  .events-hero-title {
    font-size: 2.2rem;
  }

  .events-hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .events-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .events-cta {
    padding: 3rem 2rem;
  }

  .cta-title {
    font-size: 1.8rem;
  }

  .modal-image-container {
    height: 250px;
  }

  .modal-title {
    font-size: 1.8rem;
  }

  .modal-gallery {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .events-hero {
    padding: 3rem 0;
  }

  .events-hero-title {
    font-size: 1.8rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .category-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }

  .event-image {
    height: 200px;
  }

  .event-title {
    font-size: 1.2rem;
  }

  .events-cta {
    padding: 2.5rem 1.5rem;
  }

  .cta-title {
    font-size: 1.5rem;
  }

  .cta-button {
    width: 100%;
    justify-content: center;
  }

  .modal-image-container {
    height: 200px;
  }

  .modal-title {
    font-size: 1.5rem;
  }

  .event-modal-body {
    padding: 1.5rem;
  }

  .modal-section-title {
    font-size: 1.3rem;
  }

  .modal-gallery {
    grid-template-columns: 1fr;
  }

  .modal-cta-button {
    width: 100%;
    justify-content: center;
  }
}
