/* Futuristic Theme for Sports Portfolio Website */
:root {
  /* Futuristic Color Palette */
  --futuristic-dark: #0a0e17; /* Dark background */
  --futuristic-dark-alt: #141824; /* Slightly lighter dark for cards */
  --futuristic-accent: #0062ff; /* Bright blue accent */
  --futuristic-accent-alt: #00a3ff; /* Secondary blue accent */
  --futuristic-highlight: #ff3a5e; /* Vibrant red highlight */
  --futuristic-text: #ffffff; /* White text */
  --futuristic-text-secondary: #b3c5ef; /* Light blue secondary text */
  --futuristic-gradient: linear-gradient(135deg, #0062ff, #6e00ff); /* Blue to purple gradient */
  --futuristic-gradient-alt: linear-gradient(135deg, #ff3a5e, #ff8f6b); /* Red to orange gradient */
  --futuristic-glow: 0 0 15px rgba(0, 98, 255, 0.5); /* Blue glow effect */
  --futuristic-glow-red: 0 0 15px rgba(255, 58, 94, 0.5); /* Red glow effect */

  /* Enhanced shadows */
  --futuristic-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  --futuristic-shadow-intense: 0 15px 40px rgba(0, 0, 0, 0.5);
  --futuristic-shadow-text: 0 2px 5px rgba(0, 0, 0, 0.5);

  /* Borders and radius */
  --futuristic-border-radius: 12px;
  --futuristic-border-radius-lg: 20px;
  --futuristic-border: 1px solid rgba(255, 255, 255, 0.1);

  /* Animations */
  --futuristic-transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

/* Futuristic Background */
.futuristic-bg {
  background-color: var(--futuristic-dark);
  background-image:
    radial-gradient(circle at 10% 10%, rgba(0, 98, 255, 0.1), transparent 30%),
    radial-gradient(circle at 90% 90%, rgba(255, 58, 94, 0.1), transparent 30%);
  position: relative;
  overflow: hidden;
}

.futuristic-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230062ff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
  z-index: 0;
  pointer-events: none;
}

/* Futuristic Section */
.futuristic-section {
  padding: 6rem 2rem;
  position: relative;
  z-index: 1;
}

/* Futuristic Title */
.futuristic-title {
  font-size: 3rem;
  font-weight: 800;
  color: var(--futuristic-text);
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: var(--futuristic-shadow-text);
}

.futuristic-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--futuristic-gradient);
  border-radius: 2px;
}

/* Futuristic Subtitle */
.futuristic-subtitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--futuristic-text);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.futuristic-subtitle::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 25px;
  background: var(--futuristic-accent);
  border-radius: 2px;
}

/* Futuristic Description */
.futuristic-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--futuristic-text-secondary);
  max-width: 800px;
  margin: 0 auto 3rem;
  text-align: center;
}

/* Futuristic Card */
.futuristic-card {
  background-color: var(--futuristic-dark-alt);
  border-radius: var(--futuristic-border-radius);
  border: var(--futuristic-border);
  overflow: hidden;
  box-shadow: var(--futuristic-shadow);
  transition: var(--futuristic-transition);
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.futuristic-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--futuristic-shadow-intense), 0 0 20px rgba(0, 98, 255, 0.3);
  border-color: rgba(0, 98, 255, 0.3);
}

.futuristic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 98, 255, 0.1), transparent);
  opacity: 0;
  transition: var(--futuristic-transition);
  z-index: -1;
}

.futuristic-card:hover::before {
  opacity: 1;
}

/* Futuristic Card Content */
.futuristic-card-content {
  padding: 1.8rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.futuristic-card-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--futuristic-text);
  margin-bottom: 1rem;
  transition: var(--futuristic-transition);
}

.futuristic-card:hover .futuristic-card-title {
  color: var(--futuristic-accent-alt);
}

.futuristic-card-description {
  font-size: 1rem;
  line-height: 1.7;
  color: var(--futuristic-text-secondary);
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

/* Futuristic Button */
.futuristic-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem 1.8rem;
  background: var(--futuristic-gradient);
  color: var(--futuristic-text);
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--futuristic-transition);
  box-shadow: var(--futuristic-shadow);
  position: relative;
  overflow: hidden;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  gap: 0.5rem;
}

.futuristic-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--futuristic-shadow-intense), var(--futuristic-glow);
}

.futuristic-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
  z-index: -1;
}

.futuristic-button:hover::before {
  left: 100%;
}

/* Futuristic Video Container */
.futuristic-video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: var(--futuristic-border-radius) var(--futuristic-border-radius) 0 0;
}

.futuristic-video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  z-index: 1;
}

.futuristic-video-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), transparent);
  z-index: 2;
  pointer-events: none;
}

/* Futuristic Grid */
.futuristic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2.5rem;
  margin: 3rem 0;
}

/* Futuristic Featured Section */
.futuristic-featured {
  margin: 0 auto 5rem;
  background-color: var(--futuristic-dark-alt);
  border-radius: var(--futuristic-border-radius-lg);
  overflow: hidden;
  box-shadow: var(--futuristic-shadow-intense);
  max-width: 1000px;
  position: relative;
  border: var(--futuristic-border);
}

.futuristic-featured::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: var(--futuristic-gradient);
  z-index: -1;
  border-radius: calc(var(--futuristic-border-radius-lg) + 5px);
  opacity: 0.5;
  filter: blur(10px);
}

.futuristic-featured-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: var(--futuristic-gradient);
  color: white;
  position: relative;
  overflow: hidden;
}

.futuristic-featured-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
}

.futuristic-featured-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.futuristic-featured-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  color: white;
  padding: 0.5rem 1.2rem;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Futuristic Tags */
.futuristic-tags {
  display: flex;
  gap: 0.8rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.futuristic-tag {
  background-color: rgba(0, 98, 255, 0.1);
  color: var(--futuristic-accent-alt);
  padding: 0.4rem 1rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  border: 1px solid rgba(0, 98, 255, 0.2);
}

/* Futuristic Meta */
.futuristic-meta {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 1.2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--futuristic-text-secondary);
  font-size: 0.95rem;
}

.futuristic-meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.futuristic-meta-item i {
  color: var(--futuristic-accent);
  opacity: 0.8;
}

/* Futuristic Services */
.futuristic-services {
  margin-top: 5rem;
}

.futuristic-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.futuristic-service-card {
  background-color: var(--futuristic-dark-alt);
  border-radius: var(--futuristic-border-radius);
  padding: 2rem;
  text-align: center;
  transition: var(--futuristic-transition);
  border: var(--futuristic-border);
  position: relative;
  overflow: hidden;
}

.futuristic-service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--futuristic-shadow-intense), 0 0 20px rgba(0, 98, 255, 0.2);
  border-color: rgba(0, 98, 255, 0.3);
}

.futuristic-service-icon {
  width: 80px;
  height: 80px;
  background: var(--futuristic-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
  transition: var(--futuristic-transition);
  box-shadow: var(--futuristic-glow);
}

.futuristic-service-card:hover .futuristic-service-icon {
  transform: scale(1.1) rotate(10deg);
  box-shadow: 0 0 30px rgba(0, 98, 255, 0.5);
}

.futuristic-service-title {
  font-size: 1.3rem;
  color: var(--futuristic-text);
  margin-bottom: 1rem;
  font-weight: 700;
  transition: var(--futuristic-transition);
}

.futuristic-service-card:hover .futuristic-service-title {
  color: var(--futuristic-accent-alt);
}

.futuristic-service-description {
  color: var(--futuristic-text-secondary);
  font-size: 1rem;
  line-height: 1.7;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 98, 255, 0.5);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(0, 98, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 98, 255, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(0, 98, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 98, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 98, 255, 0.5);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-40px) translateX(-10px);
  }
  75% {
    transform: translateY(-20px) translateX(10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s forwards;
  opacity: 0;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.8s forwards;
  opacity: 0;
}

.animate-fadeInRight {
  animation: fadeInRight 0.8s forwards;
  opacity: 0;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s infinite;
}

/* Particle Effects */
.futuristic-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: particleFloat linear infinite;
  opacity: 0.5;
  z-index: 0;
}

/* Interactive Elements */
.interactive-card {
  transition: var(--futuristic-transition);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.interactive-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 98, 255, 0.2), transparent);
  opacity: 0;
  transition: var(--futuristic-transition);
  z-index: 0;
}

.interactive-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--futuristic-shadow-intense), var(--futuristic-glow);
}

.interactive-card:hover::before {
  opacity: 1;
}

/* Futuristic Section Divider */
.futuristic-divider {
  height: 3px;
  background: var(--futuristic-gradient);
  margin: 4rem auto;
  width: 80%;
  max-width: 800px;
  border-radius: 3px;
  position: relative;
  box-shadow: var(--futuristic-glow);
}

.futuristic-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: var(--futuristic-accent);
  border-radius: 50%;
  box-shadow: var(--futuristic-glow);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .futuristic-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .futuristic-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 992px) {
  .futuristic-services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .futuristic-featured-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .futuristic-grid {
    grid-template-columns: 1fr;
  }

  .futuristic-services-grid {
    grid-template-columns: 1fr;
  }

  .futuristic-title {
    font-size: 2.2rem;
  }

  .futuristic-subtitle {
    font-size: 1.5rem;
  }

  .futuristic-description {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .futuristic-section {
    padding: 4rem 1.5rem;
  }

  .futuristic-card-content {
    padding: 1.5rem;
  }

  .futuristic-card-title {
    font-size: 1.2rem;
  }

  .futuristic-service-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}
