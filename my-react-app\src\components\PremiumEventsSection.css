/* Premium Events Section Styles */
.premium-events-section {
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
  padding: 0 0 5rem 0;
}

.premium-events-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Slider */
.premium-hero-slider {
  position: relative;
  width: 100%;
  height: 600px;
  overflow: hidden;
  margin-bottom: 4rem;
}

.slider-container {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.slider-slide {
  min-width: 100%;
  height: 100%;
  position: relative;
}

.slide-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 100%);
  display: flex;
  align-items: center;
}

.slide-content {
  max-width: 600px;
  padding: 0 4rem;
  color: white;
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-category {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.slide-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.slide-meta {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.slide-date, .slide-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.slide-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.slide-button {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  background-color: white;
  color: var(--primary-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.slide-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.slide-button i {
  transition: transform 0.3s ease;
}

.slide-button:hover i {
  transform: translateX(5px);
}

.slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.slider-arrow:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.slider-prev {
  left: 20px;
}

.slider-next {
  right: 20px;
}

.slider-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider-dot.active {
  background-color: white;
  transform: scale(1.2);
}

/* Events Intro */
.premium-events-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: var(--dark-color);
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.section-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 2rem;
}

/* Category Filter */
.premium-events-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.premium-category-button {
  padding: 0.8rem 1.5rem;
  background-color: white;
  color: var(--dark-color);
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.premium-category-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.premium-category-button.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.2);
}

/* Events Grid */
.premium-events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 2.5rem;
  margin: 3rem 0 5rem;
}

.premium-event-card {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  height: 100%;
  cursor: pointer;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
}

.premium-event-card.animated {
  opacity: 1;
  transform: translateY(0);
}

.premium-event-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.premium-event-image {
  height: 240px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.premium-event-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.2));
  display: flex;
  justify-content: space-between;
  padding: 1.5rem;
  color: white;
  transition: all 0.5s ease;
}

.premium-event-card:hover .premium-event-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.3));
}

.premium-event-category {
  background-color: var(--primary-color);
  color: white;
  display: inline-flex;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.premium-event-date {
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.premium-event-content {
  padding: 1.8rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.premium-event-title {
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0 0 0.8rem 0;
  color: var(--dark-color);
  line-height: 1.3;
  transition: color 0.3s ease;
}

.premium-event-card:hover .premium-event-title {
  color: var(--primary-color);
}

.premium-event-location {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.premium-event-location i {
  color: var(--primary-color);
}

.premium-event-excerpt {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.premium-event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.premium-event-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 0.4rem 0.8rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.premium-event-card:hover .premium-event-tag {
  background-color: #e6e6e6;
}

.premium-event-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.premium-stat {
  text-align: center;
}

.premium-stat-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.premium-stat-label {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.2rem;
}

.premium-event-button {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 5px 15px rgba(14, 59, 125, 0.2);
  margin-top: auto;
}

.premium-event-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(14, 59, 125, 0.3);
}

.premium-event-button i {
  transition: transform 0.3s ease;
}

.premium-event-button:hover i {
  transform: translateX(3px);
}

/* Process Section */
.premium-process-section {
  margin: 5rem 0;
  padding: 4rem 0;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.section-subtitle {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 3rem;
  text-align: center;
  color: var(--dark-color);
}

.premium-process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  padding: 0 2rem;
}

.premium-process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  border-radius: 16px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.premium-process-step:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.premium-step-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  position: relative;
  line-height: 1;
}

.premium-step-number::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.premium-step-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.premium-step-description {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
}

/* Testimonials Section */
.premium-testimonials-section {
  margin: 5rem 0;
}

.premium-testimonials {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.premium-testimonial {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.premium-testimonial:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.premium-testimonial-content {
  padding: 2.5rem;
  position: relative;
}

.premium-testimonial-content::before {
  content: '"';
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 5rem;
  font-weight: 800;
  color: rgba(var(--primary-color-rgb), 0.1);
  line-height: 1;
}

.premium-testimonial-text {
  font-size: 1rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.premium-testimonial-author {
  display: flex;
  flex-direction: column;
}

.premium-author-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 0.3rem;
}

.premium-author-title {
  font-size: 0.9rem;
  color: #666;
}

/* CTA Section */
.premium-cta-section {
  margin: 5rem 0 0;
  background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
  border-radius: 20px;
  padding: 4rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.premium-cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/events/event5.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  mix-blend-mode: overlay;
}

.premium-cta-content {
  position: relative;
  z-index: 2;
  max-width: 700px;
  margin: 0 auto;
}

.premium-cta-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
}

.premium-cta-description {
  font-size: 1.1rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
}

.premium-cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.premium-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.premium-cta-button.primary {
  background-color: white;
  color: var(--primary-color);
}

.premium-cta-button.secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.premium-cta-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.premium-cta-button i {
  transition: transform 0.3s ease;
}

.premium-cta-button:hover i {
  transform: translateX(5px);
}

/* Modal Styles */
.premium-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.premium-modal-content {
  background-color: white;
  border-radius: 20px;
  width: 100%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.premium-modal-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.premium-modal-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.premium-modal-header {
  position: relative;
}

.premium-modal-image {
  height: 350px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.premium-modal-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 3rem 2rem 2rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.premium-modal-category {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.premium-modal-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.premium-modal-meta {
  display: flex;
  gap: 1.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.premium-modal-date, .premium-modal-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.premium-modal-body {
  padding: 2.5rem;
}

.premium-modal-section {
  margin-bottom: 2.5rem;
}

.premium-modal-section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  color: var(--dark-color);
  position: relative;
  padding-bottom: 0.8rem;
}

.premium-modal-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.premium-modal-description {
  color: #555;
  font-size: 1rem;
  line-height: 1.7;
}

.premium-modal-stats-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin: 2.5rem 0;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 16px;
}

.premium-modal-stat {
  text-align: center;
}

.premium-modal-stat-value {
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.premium-modal-stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.premium-modal-highlights {
  list-style: none;
  padding: 0;
  margin: 0;
}

.premium-highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  margin-bottom: 1rem;
  color: #555;
  font-size: 1rem;
  line-height: 1.5;
}

.premium-highlight-item i {
  color: var(--primary-color);
  margin-top: 0.2rem;
}

.premium-modal-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.premium-gallery-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.premium-gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.premium-gallery-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.premium-gallery-item:hover .premium-gallery-image {
  transform: scale(1.1);
}

.premium-modal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 2.5rem;
}

.premium-modal-tag {
  background-color: #f0f0f0;
  color: #666;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
}

.premium-modal-cta {
  text-align: center;
  margin-top: 2rem;
}

.premium-modal-cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(14, 59, 125, 0.2);
}

.premium-modal-cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(14, 59, 125, 0.3);
}

.premium-modal-cta-button i {
  transition: transform 0.3s ease;
}

.premium-modal-cta-button:hover i {
  transform: translateX(5px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .premium-events-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }

  .premium-hero-slider {
    height: 500px;
  }

  .slide-content {
    max-width: 500px;
  }

  .slide-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 992px) {
  .premium-hero-slider {
    height: 450px;
  }

  .slide-content {
    max-width: 450px;
    padding: 0 3rem;
  }

  .slide-title {
    font-size: 2.2rem;
  }

  .premium-events-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .premium-modal-image {
    height: 300px;
  }

  .premium-modal-title {
    font-size: 2rem;
  }

  .premium-modal-stats-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .premium-cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .premium-cta-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .premium-hero-slider {
    height: 400px;
  }

  .slide-content {
    max-width: 100%;
    padding: 0 2rem;
  }

  .slide-overlay {
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7));
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .slide-title {
    font-size: 1.8rem;
  }

  .slide-meta {
    justify-content: center;
  }

  .premium-events-grid {
    grid-template-columns: 1fr;
  }

  .premium-process-steps {
    grid-template-columns: 1fr;
  }

  .premium-testimonials {
    grid-template-columns: 1fr;
  }

  .premium-cta-section {
    padding: 3rem 2rem;
  }

  .premium-cta-title {
    font-size: 1.8rem;
  }

  .premium-modal-image {
    height: 250px;
  }

  .premium-modal-title {
    font-size: 1.8rem;
  }

  .premium-modal-gallery {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .premium-hero-slider {
    height: 350px;
  }

  .slide-title {
    font-size: 1.5rem;
  }

  .slide-description {
    font-size: 0.9rem;
  }

  .slide-button {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }

  .premium-category-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }

  .premium-event-image {
    height: 200px;
  }

  .premium-event-title {
    font-size: 1.2rem;
  }

  .premium-cta-title {
    font-size: 1.5rem;
  }

  .premium-modal-image {
    height: 200px;
  }

  .premium-modal-title {
    font-size: 1.5rem;
  }

  .premium-modal-body {
    padding: 1.5rem;
  }

  .premium-modal-section-title {
    font-size: 1.3rem;
  }

  .premium-modal-stats-section {
    grid-template-columns: 1fr;
    padding: 1.5rem;
  }

  .premium-modal-gallery {
    grid-template-columns: 1fr;
  }
}
