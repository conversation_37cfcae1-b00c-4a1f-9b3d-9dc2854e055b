import React, { useState, useEffect, useRef } from 'react';
import './Sections.css';
import './VideographySection.css';

const VideographySection = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [featuredVideo, setFeaturedVideo] = useState(null);
  const sectionRef = useRef(null);

  useEffect(() => {
    // Set the first video as featured when component mounts
    setFeaturedVideo(featuredVideos[0]);

    // Add intersection observer for animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => observer.observe(el));
    }

    return () => {
      if (sectionRef.current) {
        const elements = sectionRef.current.querySelectorAll('.animate-on-scroll');
        elements.forEach((el) => observer.unobserve(el));
      }
    };
  }, []);

  // Featured videos with the exact YouTube links you provided
  const featuredVideos = [
    {
      id: 1,
      title: 'Siddartha Manubothu - Sports Reporting Demo Reel',
      category: 'highlights',
      description: 'A showcase of my sports reporting work covering various sporting events and interviews with athletes.',
      videoUrl: 'https://www.youtube.com/embed/JhRjDT5KNRY',
      date: 'June 15, 2023',
      views: '8.7K',
      duration: '3:45',
      tags: ['sports reporting', 'demo reel', 'broadcast journalism']
    },
    {
      id: 2,
      title: 'Siddartha Manubothu - On-Air Talent Demo',
      category: 'highlights',
      description: 'Highlights of my on-air broadcasting work including live game coverage and studio analysis.',
      videoUrl: 'https://www.youtube.com/embed/3T766dyF-Dc',
      date: 'May 22, 2023',
      views: '5.2K',
      duration: '4:12',
      tags: ['on-air talent', 'broadcasting', 'sports analysis']
    },
    {
      id: 3,
      title: 'Siddartha Manubothu - Sports Broadcasting Reel',
      category: 'broadcast',
      description: 'Professional sports broadcasting reel showcasing play-by-play commentary, analysis, and on-camera presence during live sporting events.',
      videoUrl: 'https://www.youtube.com/embed/G1kwEMIKl2w',
      date: 'April 10, 2023',
      views: '12.3K',
      duration: '5:18',
      tags: ['sports broadcasting', 'play-by-play', 'commentary']
    },
    {
      id: 4,
      title: 'Siddartha Manubothu - Sports Journalism Portfolio',
      category: 'journalism',
      description: 'A comprehensive look at my sports journalism work, featuring interviews, field reporting, and in-studio segments across various sporting events.',
      videoUrl: 'https://www.youtube.com/embed/nj7Ob0flGF4',
      date: 'March 28, 2023',
      views: '7.5K',
      duration: '6:42',
      tags: ['sports journalism', 'interviews', 'field reporting']
    },
    {
      id: 5,
      title: 'Basketball Championship Coverage',
      category: 'broadcast',
      description: 'Full coverage of the championship basketball game with play-by-play commentary and post-game analysis.',
      videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      date: 'July 5, 2023',
      views: '15.3K',
      duration: '8:22',
      tags: ['basketball', 'championship', 'live coverage']
    },
    {
      id: 6,
      title: 'Football Season Highlights',
      category: 'highlights',
      description: 'A compilation of the best moments from the football season, featuring key plays and game-changing moments.',
      videoUrl: 'https://www.youtube.com/embed/kJQP7kiw5Fk',
      date: 'August 12, 2023',
      views: '22.1K',
      duration: '7:15',
      tags: ['football', 'highlights', 'season recap']
    },
    {
      id: 7,
      title: 'Athlete Interview Series: Rising Stars',
      category: 'journalism',
      description: 'In-depth interviews with emerging athletic talents, exploring their journeys, challenges, and aspirations in the world of sports.',
      videoUrl: 'https://www.youtube.com/embed/6Dh-RL__uN4',
      date: 'September 3, 2023',
      views: '9.8K',
      duration: '10:30',
      tags: ['interviews', 'rising stars', 'athlete profiles']
    },
    {
      id: 8,
      title: 'Behind the Scenes: Game Day Production',
      category: 'journalism',
      description: 'An exclusive look at what goes into producing a professional sports broadcast, from pre-game preparation to post-game wrap-up.',
      videoUrl: 'https://www.youtube.com/embed/9bZkp7q19f0',
      date: 'October 18, 2023',
      views: '11.5K',
      duration: '12:45',
      tags: ['behind the scenes', 'production', 'game day']
    }
  ];

  // Video categories with counts
  const getCategoryCounts = () => {
    const counts = {};
    featuredVideos.forEach(video => {
      if (counts[video.category]) {
        counts[video.category]++;
      } else {
        counts[video.category] = 1;
      }
    });
    return counts;
  };

  const categoryCounts = getCategoryCounts();

  const categories = [
    { id: 'all', label: 'All Videos', count: featuredVideos.length },
    { id: 'highlights', label: 'Highlights', count: categoryCounts['highlights'] || 0 },
    { id: 'broadcast', label: 'Broadcasting', count: categoryCounts['broadcast'] || 0 },
    { id: 'journalism', label: 'Journalism', count: categoryCounts['journalism'] || 0 }
  ];

  // Filter videos based on active category
  const filteredVideos = activeCategory === 'all'
    ? featuredVideos
    : featuredVideos.filter(video => video.category === activeCategory);

  // Function to set a video as featured
  const handleSetFeatured = (video) => {
    setFeaturedVideo(video);
    // Scroll to the featured video section
    document.getElementById('featured-video-section').scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="section videography-section" id="videography" ref={sectionRef}>
      <h2 className="section-title animate-on-scroll">Sports Videography</h2>
      <div className="section-content">
        <p className="section-description animate-on-scroll">
          My sports videography captures the energy, emotion, and excitement of athletic competition.
          From dynamic highlights to in-depth documentaries, each video tells a compelling story about the world of sports.
          Explore my collection of professional sports videos showcasing my skills in broadcast journalism and video production.
        </p>

        {/* Featured Video Section */}
        <div id="featured-video-section" className="featured-video-section animate-on-scroll">
          {featuredVideo && (
            <>
              <div className="featured-video-header">
                <h3 className="featured-video-title">Featured Video</h3>
                <div className="featured-video-badge">
                  <i className="fas fa-star"></i> Editor's Pick
                </div>
              </div>
              <div className="featured-video-container">
                <iframe
                  src={featuredVideo.videoUrl}
                  title={featuredVideo.title}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
              <div className="featured-video-content">
                <div className="featured-video-info">
                  <h3 className="featured-video-name">{featuredVideo.title}</h3>
                  <p className="featured-video-description">{featuredVideo.description}</p>
                  <div className="featured-video-meta">
                    <span className="meta-item"><i className="fas fa-calendar-alt"></i> {featuredVideo.date}</span>
                    <span className="meta-item"><i className="fas fa-eye"></i> {featuredVideo.views} views</span>
                    <span className="meta-item"><i className="fas fa-clock"></i> {featuredVideo.duration}</span>
                    <span className="meta-item"><i className="fas fa-tag"></i> {featuredVideo.category}</span>
                  </div>
                  <div className="featured-video-tags">
                    {featuredVideo.tags.map((tag, index) => (
                      <span key={index} className="video-tag">#{tag}</span>
                    ))}
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Video Category Filter */}
        <div className="video-categories animate-on-scroll">
          {categories.map(category => (
            <button
              key={category.id}
              className={`category-button ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              <span className="category-name">{category.label}</span>
              <span className="category-count">{category.count}</span>
            </button>
          ))}
        </div>

        {/* Video Grid */}
        <div className="video-featured-grid animate-on-scroll">
          {filteredVideos.map(video => (
            <div key={video.id} className="video-card">
              <div className="video-container">
                <iframe
                  src={video.videoUrl}
                  title={video.title}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
              <div className="video-content">
                <div className="video-header">
                  <span className="video-date">{video.date}</span>
                  <span className="video-duration"><i className="fas fa-clock"></i> {video.duration}</span>
                </div>
                <h3 className="video-title">{video.title}</h3>
                <p className="video-description">{video.description}</p>
                <div className="video-tags">
                  {video.tags.slice(0, 2).map((tag, index) => (
                    <span key={index} className="video-tag">#{tag}</span>
                  ))}
                </div>
                <div className="video-meta">
                  <span className="video-views"><i className="fas fa-eye"></i> {video.views} views</span>
                  <span className="video-category"><i className="fas fa-tag"></i> {video.category}</span>
                </div>
                <button
                  className="set-featured-btn"
                  onClick={() => handleSetFeatured(video)}
                >
                  <i className="fas fa-star"></i> Set as Featured
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Video Production Services */}
        <div className="video-services animate-on-scroll">
          <h3 className="section-subtitle">Video Production Services</h3>
          <div className="services-grid">
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-video"></i>
              </div>
              <h4 className="service-title">Game Coverage</h4>
              <p className="service-description">
                Professional coverage of sporting events with multiple camera angles and dynamic editing.
              </p>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-film"></i>
              </div>
              <h4 className="service-title">Highlight Reels</h4>
              <p className="service-description">
                Compelling highlight packages that showcase the best moments and plays.
              </p>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-user"></i>
              </div>
              <h4 className="service-title">Athlete Profiles</h4>
              <p className="service-description">
                In-depth video profiles that tell the stories of athletes and their journeys.
              </p>
            </div>
            <div className="service-card">
              <div className="service-icon">
                <i className="fas fa-camera"></i>
              </div>
              <h4 className="service-title">Cinematic Sports Films</h4>
              <p className="service-description">
                Artistic sports films with cinematic quality and storytelling.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default VideographySection;
